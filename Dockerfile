# 基础镜像
FROM openresty/openresty:********-3-alpine

# 删除默认配置文件
RUN rm -f /etc/nginx/conf.d/default.conf

# 创建挂载配置目录（可选）
RUN mkdir -p /etc/nginx/conf.d

# 复制 dist.zip 到容器中
#RUN mkdir -p /usr/share/nginx/html
COPY ./apps/web-antd/dist/ /usr/share/nginx/html/

# 如果有配置文件需要复制，按需取消下面注释
# COPY conf/nginx.conf /usr/local/openresty/nginx/conf/nginx.conf
# COPY conf/conf.d/ /etc/nginx/conf.d/

# 开放80端口
EXPOSE 80

# 启动命令
CMD ["openresty", "-g", "daemon off;"]
