import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import type {
  BankReceiptData,
  BankReceiptQueryParams,
  InvoiceData,
  InvoiceQueryParams,
  PayrollData,
  PayrollQueryParams,
} from '#/api/jsj-ai/types';
import {
  fetchBankReceiptList,
  fetchInvoiceList,
  fetchPayrollList,
} from '#/api/jsj-ai/api-v2';

export function useOriginalVoucherData() {
  // 数据源
  const tableData = ref<(BankReceiptData | InvoiceData | PayrollData)[]>([]);
  const originalBankData = ref<BankReceiptData[]>([]); // 存储银行回单原始数据，用于前端筛选
  const loading = ref(false);

  // 分页配置
  const pagination = ref({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  // 选中状态
  const selectedRowKeys = ref<string[]>([]);
  const selectedInvoices = ref<InvoiceData[]>([]);

  // 表格行选择配置
  const rowSelection = computed(() => ({
    onChange: (keys: any[], rows: any[]) => {
      selectedRowKeys.value = keys.map(String);
      selectedInvoices.value = rows;
    },
    onSelectAll: (
      selected: boolean,
      selectedRows: any[],
      changeRows: any[],
    ) => {
      console.log('全选变化:', selected, selectedRows, changeRows);
    },
    selectedRowKeys: selectedRowKeys.value,
    type: 'checkbox' as const,
  }));

  // 前端筛选银行回单数据
  const filterBankReceiptData = (formData: Record<string, any>) => {
    let filteredData = [...originalBankData.value];

    // 银行名称筛选
    if (formData.bank_name && formData.bank_name !== '') {
      filteredData = filteredData.filter(
        (item) => item.bank_name === formData.bank_name,
      );
    }

    // 搜索文本筛选（摘要/户名/备注）
    if (formData.search_text && formData.search_text.trim() !== '') {
      const searchText = formData.search_text.trim().toLowerCase();
      filteredData = filteredData.filter(
        (item) =>
          (item.summary && item.summary.toLowerCase().includes(searchText)) ||
          (item.counterparty_account_name &&
            item.counterparty_account_name
              .toLowerCase()
              .includes(searchText)) ||
          (item.note && item.note.toLowerCase().includes(searchText)),
      );
    }

    tableData.value = filteredData;
    pagination.value.total = filteredData.length;
    
    // 如果当前页超出范围，重置到第一页
    if (
      pagination.value.current >
      Math.ceil(filteredData.length / pagination.value.pageSize)
    ) {
      pagination.value.current = 1;
    }
  };

  // 查询银行回单数据
  const fetchBankReceiptData = async (params: BankReceiptQueryParams) => {
    loading.value = true;
    try {
      // 过滤掉空值参数
      Object.keys(params).forEach((key) => {
        const value = params[key as keyof BankReceiptQueryParams];
        if (value === undefined || value === null || value === '') {
          delete params[key as keyof BankReceiptQueryParams];
        }
      });

      const result = await fetchBankReceiptList(params);

      // 保存原始数据
      originalBankData.value = result || [];
      console.log('获取银行回单数据成功:', result?.length || 0, '条记录');

      // 应用前端筛选
      filterBankReceiptData({});

      return true;
    } catch (error: any) {
      console.error('银行回单查询失败:', error);
      message.error(error?.message || '银行回单查询失败');
      originalBankData.value = [];
      tableData.value = [];
      pagination.value.total = 0;
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 查询工资单数据
  const fetchPayrollData = async (params: PayrollQueryParams) => {
    loading.value = true;
    try {
      // 过滤掉空值参数
      Object.keys(params).forEach((key) => {
        const value = params[key as keyof PayrollQueryParams];
        if (value === undefined || value === null || value === '') {
          delete params[key as keyof PayrollQueryParams];
        }
      });

      const result = await fetchPayrollList(params);

      tableData.value = result || [];
      console.log('获取工资单数据成功:', result?.length || 0, '条记录');

      // 更新分页信息
      pagination.value.total = tableData.value.length;
      if (
        pagination.value.current >
        Math.ceil(tableData.value.length / pagination.value.pageSize)
      ) {
        pagination.value.current = 1;
      }

      return true;
    } catch (error: any) {
      console.error('工资单查询失败:', error);
      message.error(error?.message || '工资单查询失败');
      tableData.value = [];
      pagination.value.total = 0;
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 查询发票数据
  const fetchInvoiceData = async (params: InvoiceQueryParams) => {
    loading.value = true;
    try {
      // 过滤掉空值参数
      Object.keys(params).forEach((key) => {
        const value = params[key as keyof InvoiceQueryParams];
        if (value === undefined || value === null || value === '') {
          delete params[key as keyof InvoiceQueryParams];
        }
      });

      const result = await fetchInvoiceList(params);

      tableData.value = result || [];
      console.log('获取发票数据成功:', result?.length || 0, '条记录');

      // 更新分页信息
      pagination.value.total = tableData.value.length;
      if (
        pagination.value.current >
        Math.ceil(tableData.value.length / pagination.value.pageSize)
      ) {
        pagination.value.current = 1;
      }

      return true;
    } catch (error: any) {
      console.error('发票查询失败:', error);
      message.error(error?.message || '发票查询失败');
      tableData.value = [];
      pagination.value.total = 0;
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 清空选中状态
  const clearSelectedInvoices = () => {
    selectedInvoices.value = [];
    selectedRowKeys.value = [];
  };

  // 分页处理方法
  const handlePaginationChange = (page: number, pageSize: number) => {
    pagination.value.current = page;
    pagination.value.pageSize = pageSize;
  };

  const handlePageSizeChange = (current: number, size: number) => {
    pagination.value.current = 1; // 重置到第一页
    pagination.value.pageSize = size;
  };

  // 监听表格数据变化，清空选中状态
  watch(tableData, () => {
    clearSelectedInvoices();
  });

  return {
    // 数据
    tableData,
    originalBankData,
    loading,
    pagination,
    selectedRowKeys,
    selectedInvoices,
    rowSelection,
    
    // 方法
    filterBankReceiptData,
    fetchBankReceiptData,
    fetchPayrollData,
    fetchInvoiceData,
    clearSelectedInvoices,
    handlePaginationChange,
    handlePageSizeChange,
  };
}
