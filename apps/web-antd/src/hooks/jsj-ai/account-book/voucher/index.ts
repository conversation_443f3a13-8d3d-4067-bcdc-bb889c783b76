/**
 * 摘要选择逻辑
 */
import type { AuxiliaryItm, SummaryItmData } from '#/api/account-book/bookkeeping';
import type {
  AccountSubjectItem,
  AssistantAccountingItem,
  AccountSubjectQueryParams,
  AssistantAccountingQueryParams
} from '#/api/jsj-ai/types';

import { computed, onMounted, ref, watch } from 'vue';

import { getAuxiliary, getLedger, getSummary } from '#/api/account-book/bookkeeping';
import {
  fetchAccountSubjectList,
  fetchAssistantAccountingList
} from '#/api/jsj-ai/api-v2';
import { useCompanySelection } from '../../ai-chat/useCompanySelection';

// 全局缓存
const accountSubjectsCache = new Map<string, AccountSubjectItem[]>();
const assistantAccountingCache = new Map<string, Record<string, AssistantAccountingItem[]>>();
const cacheExpiry = new Map<string, number>();
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

const abstractselectdata = ref<SummaryItmData>([]);
const subjectselectdata = ref<SummaryItmData>([]);
const auxiliaryselectdata = ref<AuxiliaryItm[]>([]);
// 摘要数据
export const useAbstractData = () => {
    const fetchData = async () => {
        const res = await getSummary();
        if (res.returnCode === '200') {
            abstractselectdata.value = res.data;
        }
    };
    onMounted(() => {
        if (abstractselectdata.value.length === 0) {
            fetchData();
        }
    });
    return {
        selectdata: abstractselectdata,
        fetchData,
    };
};
// 科目数据
export const useSubjectData = () => {
    const fetchData = async () => {
        const res = await getLedger();
        if (res.returnCode === '200') {
            subjectselectdata.value = res.data;
        }
    };
    onMounted(() => {
        if (subjectselectdata.value.length === 0) {
            fetchData();
        }
    });
    return {
        selectdata: subjectselectdata,
        fetchData,
    };
};
// 供应商公司数据
export const useAuxiliaryData = () => {
    const fetchData = async () => {
        const res = await getAuxiliary();
        if (res.returnCode === '200') {
            auxiliaryselectdata.value = res.data;
        }
    };
    onMounted(() => {
        if (auxiliaryselectdata.value.length === 0) {
            fetchData();
        }
    });
    return {
        selectdata: auxiliaryselectdata,
        fetchData,
    };
};

// 会计科目选择器数据接口
export interface AccountSubjectOption {
  value: string; // 科目ID
  label: string; // 显示文本：科目代码 + 科目名称 + 辅助核算信息
  code: string; // 科目代码
  name: string; // 科目名称
  useAssistant: boolean; // 是否启用辅助核算
  assistantType?: string; // 辅助核算类型
  assistantOptions?: AssistantAccountingItem[]; // 辅助核算选项
}

// 辅助核算类型映射
const ASSISTANT_TYPE_MAP = {
  s: 'supplier',    // 供应商
  c: 'customer',    // 客户
  i: 'inventory',   // 存货
  p: 'project',     // 项目
  d: 'department',  // 部门
  e: 'employee',    // 员工
} as const;

/**
 * 会计科目选择器 hooks
 * 提供格式化的会计科目选项，包含辅助核算信息
 */
export const useAccountSubjects = () => {
  // 安全地获取公司选择
  let selectedCompany: any;
  try {
    const companySelection = useCompanySelection();
    selectedCompany = companySelection.selectedCompany;
  } catch (error) {
    console.error('获取公司选择失败:', error);
    // 创建一个默认的 ref
    selectedCompany = ref('');
  }

  // 原始数据
  const accountSubjects = ref<AccountSubjectItem[]>([]);
  const assistantAccounting = ref<Record<string, AssistantAccountingItem[]>>({});
  const loading = ref(false);
  const error = ref<string | null>(null);

  /**
   * 递归提取所有最底层科目（叶子节点）
   */
  const extractLeafSubjects = (subjects: AccountSubjectItem[]): AccountSubjectItem[] => {
    const leafSubjects: AccountSubjectItem[] = [];

    const traverse = (items: AccountSubjectItem[]) => {
      for (const item of items) {
        if (item.children && item.children.length > 0) {
          // 有子节点，继续递归
          traverse(item.children);
        } else {
          // 叶子节点，添加到结果中
          leafSubjects.push(item);
        }
      }
    };

    traverse(subjects);
    return leafSubjects;
  };

  /**
   * 格式化科目显示文本
   */
  const formatSubjectLabel = (
    subject: AccountSubjectItem,
    assistantItem?: AssistantAccountingItem
  ): string => {
    let label = `${subject.code} ${subject.name}`;

    if (assistantItem) {
      label += ` ${assistantItem.code} ${assistantItem.name}`;
    }

    return label;
  };

  /**
   * 获取辅助核算选项
   */
  const getAssistantOptions = (assistantType: string): AssistantAccountingItem[] => {
    const mappedType = ASSISTANT_TYPE_MAP[assistantType as keyof typeof ASSISTANT_TYPE_MAP];
    return assistantAccounting.value[mappedType] || [];
  };

  /**
   * 生成科目选项列表
   */
  const subjectOptions = computed<AccountSubjectOption[]>(() => {
    const allSubjects = extractLeafSubjects(accountSubjects.value);
    const options: AccountSubjectOption[] = [];

    for (const subject of allSubjects) {
      if (subject.useAssistant && subject.assistantType) {
        // 启用辅助核算的科目
        const assistantOptions = getAssistantOptions(subject.assistantType);

        if (assistantOptions.length > 0) {
          // 为每个辅助核算项创建一个选项
          for (const assistantItem of assistantOptions) {
            options.push({
              value: `${subject.id}_${assistantItem.id}`, // 组合ID
              label: formatSubjectLabel(subject, assistantItem),
              code: subject.code,
              name: subject.name,
              useAssistant: true,
              assistantType: subject.assistantType,
              assistantOptions: [assistantItem],
            });
          }
        } else {
          // 辅助核算选项为空，只显示科目本身
          options.push({
            value: subject.id.toString(),
            label: formatSubjectLabel(subject),
            code: subject.code,
            name: subject.name,
            useAssistant: true,
            assistantType: subject.assistantType,
            assistantOptions: [],
          });
        }
      } else {
        // 未启用辅助核算的科目
        options.push({
          value: subject.id.toString(),
          label: formatSubjectLabel(subject),
          code: subject.code,
          name: subject.name,
          useAssistant: false,
        });
      }
    }

    return options;
  });

  /**
   * 检查缓存是否有效
   */
  const isCacheValid = (cacheKey: string): boolean => {
    const expiry = cacheExpiry.get(cacheKey);
    return expiry ? Date.now() < expiry : false;
  };

  /**
   * 设置缓存过期时间
   */
  const setCacheExpiry = (cacheKey: string): void => {
    cacheExpiry.set(cacheKey, Date.now() + CACHE_DURATION);
  };

  /**
   * 获取会计科目数据
   */
  const fetchAccountSubjects = async () => {
    if (!selectedCompany?.value) {
      error.value = '请先选择公司';
      return;
    }

    const cacheKey = `subjects_${selectedCompany.value}`;

    // 检查缓存
    if (isCacheValid(cacheKey) && accountSubjectsCache.has(cacheKey)) {
      accountSubjects.value = accountSubjectsCache.get(cacheKey)!;
      return;
    }

    loading.value = true;
    error.value = null;

    try {
      const params: AccountSubjectQueryParams = {
        company_name: selectedCompany.value,
      };

      // 使用新的统一API函数
      const response = await fetchAccountSubjectList(params);

      // 现在response直接是AccountSubjectResponseData类型
      if (response?.subjects) {
        const subjectsData = response.subjects;

        // 合并所有分类的科目数据
        const allSubjects: AccountSubjectItem[] = [
          ...(subjectsData.assets || []),
          ...(subjectsData.cost || []),
          ...(subjectsData.equity || []),
          ...(subjectsData.liabilities || []),
          ...(subjectsData.profit || []),
        ];

        // 更新数据和缓存
        accountSubjects.value = allSubjects;
        accountSubjectsCache.set(cacheKey, allSubjects);
        setCacheExpiry(cacheKey);
      } else {
        error.value = '获取会计科目数据格式错误';
      }
    } catch (err) {
      console.error('获取会计科目失败:', err);
      error.value = '获取会计科目失败';
    } finally {
      loading.value = false;
    }
  };

  /**
   * 获取辅助核算数据
   */
  const fetchAssistantAccounting = async () => {
    if (!selectedCompany?.value) {
      return;
    }

    const cacheKey = `assistant_${selectedCompany.value}`;

    // 检查缓存
    if (isCacheValid(cacheKey) && assistantAccountingCache.has(cacheKey)) {
      assistantAccounting.value = assistantAccountingCache.get(cacheKey)!;
      return;
    }

    try {
      const params: AssistantAccountingQueryParams = {
        company_name: selectedCompany.value,
      };

      // 使用新的统一API函数
      const response = await fetchAssistantAccountingList(params);

      // 现在response直接是AssistantAccountingResponseData类型
      if (response?.assistant_accounting) {
        // 更新数据和缓存
        assistantAccounting.value = response.assistant_accounting as unknown as Record<string, AssistantAccountingItem[]>;
        assistantAccountingCache.set(cacheKey, response.assistant_accounting as unknown as Record<string, AssistantAccountingItem[]>);
        setCacheExpiry(cacheKey);
      }
    } catch (err) {
      console.error('获取辅助核算数据失败:', err);
    }
  };

  /**
   * 初始化数据
   */
  const initData = async () => {
    await Promise.all([
      fetchAccountSubjects(),
      fetchAssistantAccounting(),
    ]);
  };

  /**
   * 清除缓存
   */
  const clearCache = (companyName?: string) => {
    if (companyName) {
      // 清除指定公司的缓存
      const subjectKey = `subjects_${companyName}`;
      const assistantKey = `assistant_${companyName}`;
      accountSubjectsCache.delete(subjectKey);
      assistantAccountingCache.delete(assistantKey);
      cacheExpiry.delete(subjectKey);
      cacheExpiry.delete(assistantKey);
    } else {
      // 清除所有缓存
      accountSubjectsCache.clear();
      assistantAccountingCache.clear();
      cacheExpiry.clear();
    }
  };

  /**
   * 刷新数据（强制重新获取）
   */
  const refreshData = async () => {
    // 清除当前公司的缓存
    if (selectedCompany?.value) {
      clearCache(selectedCompany.value);
    }
    await initData();
  };

  // 监听公司变化，自动获取数据（安全检查）
  try {
    watch(
      () => selectedCompany?.value,
      (newCompany, oldCompany) => {
        if (newCompany && newCompany !== oldCompany) {
          initData();
        }
      },
      { immediate: true }
    );
  } catch (error) {
    console.error('监听公司变化失败:', error);
  }

  // 组件挂载时自动获取数据
  onMounted(() => {
    try {
      if (selectedCompany?.value) {
        initData();
      }
    } catch (error) {
      console.error('组件挂载时初始化数据失败:', error);
    }
  });

  return {
    // 数据
    subjectOptions,
    accountSubjects,
    assistantAccounting,

    // 状态
    loading,
    error,

    // 方法
    fetchAccountSubjects,
    fetchAssistantAccounting,
    initData,
    refreshData,
    clearCache,

    // 工具方法
    extractLeafSubjects,
    formatSubjectLabel,
    getAssistantOptions,
  };
};
