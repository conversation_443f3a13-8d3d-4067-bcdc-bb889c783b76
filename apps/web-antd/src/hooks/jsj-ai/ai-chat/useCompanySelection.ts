import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { getUserCustomerNames } from '#/api/jsj-ai/api-v2';
import { useCompanySelectionStore, type CompanyInfo } from '#/store/modules/company-selection';
import { useMonthSelectionStore } from '#/store/modules/month-selection';
import { useUserStore } from '@vben/stores';

export function useCompanySelection() {
  const companySelectionStore = useCompanySelectionStore();
  const monthSelectionStore = useMonthSelectionStore();
  const router = useRouter();
  const userStore = useUserStore();

  const companyList = ref<CompanyInfo[]>([]);
  const isInitializing = ref(true);
  const initializationError = ref<string | null>(null);

  // 获取公司列表（带缓存）
  const fetchCompanyNames = async (forceRefresh = false) => {
    try {
      isInitializing.value = true;
      initializationError.value = null;

      const username = userStore.userInfo?.username || '';
      const tenant_id = userStore.userInfo?.tenantId || '';

      if (!username || !tenant_id) {
        initializationError.value = '缺少用户信息或租户ID';
        isInitializing.value = false;
        return;
      }

      // 清理过期缓存
      companySelectionStore.clearExpiredCache();

      // 检查缓存是否有效且不强制刷新
      if (!forceRefresh && companySelectionStore.isCacheValid(username, tenant_id)) {
        console.log('✅ 使用缓存的公司数据');
        const cachedCompanies = companySelectionStore.getCompanyListCache();
        const cacheInfo = companySelectionStore.getUserCustomerDataCache();
        console.log('📦 缓存信息:', {
          公司数量: cachedCompanies.length,
          缓存时间: cacheInfo ? new Date(cacheInfo.cached_at).toLocaleString() : '未知',
          用户: cacheInfo?.username,
          租户: cacheInfo?.tenant_id
        });
        if (cachedCompanies.length > 0) {
          companyList.value = cachedCompanies;

          // 验证当前选中的公司是否仍然有效
          const currentSelected = companySelectionStore.getSelectedCompany();
          const isCurrentSelectedValid = companyList.value.some(
            (company) => company.name === currentSelected,
          );

          if (!isCurrentSelectedValid && companyList.value[0]) {
            companySelectionStore.setSelectedCompany(companyList.value[0].name);
          }

          isInitializing.value = false;
          return;
        }
      }

      // 从API获取数据
      console.log('🌐 从API获取公司数据', { username, tenant_id });
      const userData = await getUserCustomerNames({ username, tenant_id });
      console.log('📡 API响应:', userData);

      if (userData?.yqdz?.customer_names) {
        const customer_names = userData.yqdz.customer_names;

        if (customer_names && Array.isArray(customer_names)) {
          // 转换数据格式
          const companies: CompanyInfo[] = customer_names.map((name: string) => ({
            id: name,
            name,
          }));

          // 更新本地状态
          companyList.value = companies;

          // 缓存数据
          companySelectionStore.setCompanyListCache(companies);
          companySelectionStore.setUserCustomerDataCache({
            username,
            tenant_id,
            data_source: userData.data_source,
            saas_id: userData.saas_id,
            company_id: userData.yqdz.company_id,
            account_id: userData.yqdz.account_id,
            customer_names,
            cached_at: Date.now(),
          });

        console.log('💾 公司数据已缓存:', {
          公司数量: companies.length,
          公司列表: companies.map(c => c.name),
          缓存时间: new Date().toLocaleString()
        });

        // 处理选中的公司
        if (companies.length > 0) {
          const currentSelected = companySelectionStore.getSelectedCompany();
          const isCurrentSelectedValid = companies.some(
            (company) => company.name === currentSelected,
          );

          if (isCurrentSelectedValid) {
            console.log('保持当前选中的公司:', currentSelected);
          } else {
            companySelectionStore.setSelectedCompany(companies[0]!.name);
            console.log('默认选择公司:', companies[0]!.name);
          }
        }

          isInitializing.value = false;
        } else {
          initializationError.value = '客户列表数据格式不正确';
          isInitializing.value = false;
        }
      } else {
        initializationError.value = '获取用户数据失败';
        isInitializing.value = false;
      }
    } catch (error) {
      console.error('获取公司列表失败:', error);
      initializationError.value = '获取客户列表失败，请检查网络连接';
      isInitializing.value = false;

      // 尝试使用缓存数据作为备用
      const cachedCompanies = companySelectionStore.getCompanyListCache();
      if (cachedCompanies.length > 0) {
        console.log('API失败，使用缓存数据作为备用');
        companyList.value = cachedCompanies;
        initializationError.value = '网络连接失败，使用缓存数据';
      } else {
        // 如果没有缓存，使用默认数据
        companyList.value = [{ id: 'default', name: '默认公司' }];
        companySelectionStore.setSelectedCompany('默认公司');
      }
    }
  };

  // 检查是否在AI凭证页面并重新加载数据
  const checkAndReloadVoucherData = () => {
    const currentRoute = router.currentRoute.value;
    if (currentRoute.path === '/bookkeeping/view') {
      window.dispatchEvent(new CustomEvent('reload-voucher-data'));
    }
  };

  // 处理公司选择变化
  const handleCompanyChange = (value: any, onUpdate: () => void) => {
    if (typeof value === 'string') {
      companySelectionStore.setSelectedCompany(value);
      onUpdate();
      checkAndReloadVoucherData();
    }
  };

  // 处理月份选择变化
  const handleMonthChange = (date: any, onUpdate: () => void) => {
    if (typeof date === 'string') {
      monthSelectionStore.setSelectedMonth(dayjs(date));
    } else if (date === null) {
      monthSelectionStore.setSelectedMonth(dayjs());
    } else if (date) {
      monthSelectionStore.setSelectedMonth(date);
    }
    onUpdate();
    checkAndReloadVoucherData();
  };

  // 处理月份选择变化（Select组件）
  const handleMonthSelectChange = (value: string, onUpdate: () => void) => {
    monthSelectionStore.setSelectedMonth(dayjs(value, 'YYYYMM'));
    onUpdate();
    checkAndReloadVoucherData();
  };

  // 生成月份选项
  const monthOptions = computed(() => {
    const options = [];
    const currentDate = dayjs();

    for (let i = 0; i < 12; i++) {
      const date = currentDate.subtract(i, 'month');
      options.push({
        label: date.format('YYYY年MM月'),
        value: date.format('YYYYMM'),
      });
    }

    return options;
  });

  // 当前选中的月份
  const selectedMonth = computed({
    get: () => monthSelectionStore.getFormattedMonth(),
    set: (value: string) => {
      monthSelectionStore.setSelectedMonth(dayjs(value, 'YYYYMM'));
    },
  });

  const selectedCompany = computed({
    get: () => companySelectionStore.getSelectedCompany(),
    set: (value: string) => {
      companySelectionStore.setSelectedCompany(value);
    },
  });

  // 强制刷新公司数据（清除缓存并重新获取）
  const refreshCompanyNames = async () => {
    console.log('强制刷新公司数据');
    companySelectionStore.clearCache();
    await fetchCompanyNames(true);
  };

  // 清除缓存
  const clearCompanyCache = () => {
    console.log('清除公司数据缓存');
    companySelectionStore.clearCache();
  };

  return {
    companyList,
    isInitializing,
    initializationError,
    selectedCompany,
    selectedMonth,
    monthOptions,
    fetchCompanyNames,
    refreshCompanyNames,
    clearCompanyCache,
    handleCompanyChange,
    handleMonthChange,
    handleMonthSelectChange,
    checkAndReloadVoucherData,
  };
}
