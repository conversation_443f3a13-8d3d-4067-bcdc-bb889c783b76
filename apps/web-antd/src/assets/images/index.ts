/**
 * 图片资源统一管理
 * 所有图片资源的导入和导出都在这里统一管理
 */

import LogoJsj from './logos/logo_jsj.png';
import LogoText from './logos/logo-text.png';
import ReturnIcon from './icons/return.png';

export {
  LogoJsj,
  LogoText,
  ReturnIcon,
};

// AI 相关图标
export { default as DoubaoAiIcon } from './ai/doubao-ai-icon.svg';
export { default as DoubaoAiIconModern } from './ai/doubao-ai-icon-modern.svg';
export { default as DoubaoAiIconRefined } from './ai/doubao-ai-icon-refined.svg';

// 背景图片
export { default as LoginBackground } from './backgrounds/login.png';

// 功能图标
export { default as IconC } from './icons/C.png';
export { default as IconBiaozhu } from './icons/icon_biaozhu.png';
export { default as IconChakan } from './icons/icon_chakan.png';
export { default as IconCharu } from './icons/icon_charu.png';
export { default as IconChongxiao } from './icons/icon_chongxiao.png';
export { default as IconDayin } from './icons/icon_dayin.png';
export { default as IconShanchu } from './icons/icon_shanchu.png';
export { default as IconXiugai } from './icons/icon_xiugai.png';

// 图片资源路径常量
export const IMAGE_PATHS = {
  logos: {
    jsj: '/src/assets/images/logos/logo_jsj.png',
    text: '/src/assets/images/logos/logo-text.png',
  },
  ai: {
    doubao: '/src/assets/images/ai/doubao-ai-icon.svg',
    doubaoModern: '/src/assets/images/ai/doubao-ai-icon-modern.svg',
    doubaoRefined: '/src/assets/images/ai/doubao-ai-icon-refined.svg',
  },
  backgrounds: {
    login: '/src/assets/images/backgrounds/login.png',
  },
  icons: {
    c: '/src/assets/images/icons/C.png',
    biaozhu: '/src/assets/images/icons/icon_biaozhu.png',
    chakan: '/src/assets/images/icons/icon_chakan.png',
    charu: '/src/assets/images/icons/icon_charu.png',
    chongxiao: '/src/assets/images/icons/icon_chongxiao.png',
    dayin: '/src/assets/images/icons/icon_dayin.png',
    shanchu: '/src/assets/images/icons/icon_shanchu.png',
    xiugai: '/src/assets/images/icons/icon_xiugai.png',
  },
} as const;
