<template>
  <div class="h-full p-6">
    <div class="rounded-lg bg-white p-6 shadow-sm">
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">AI客服 - 待办中心</h1>
        <p class="mt-2 text-gray-600">管理客户咨询和待办事项</p>
      </div>

      <!-- 统计卡片 -->
      <div class="mb-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <div class="rounded-lg bg-blue-50 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <MessageOutlined class="h-8 w-8 text-blue-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-blue-600">待处理咨询</p>
              <p class="text-2xl font-bold text-blue-900">12</p>
            </div>
          </div>
        </div>

        <div class="rounded-lg bg-green-50 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <CheckCircleOutlined class="h-8 w-8 text-green-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-green-600">已完成</p>
              <p class="text-2xl font-bold text-green-900">45</p>
            </div>
          </div>
        </div>

        <div class="rounded-lg bg-yellow-50 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <ClockCircleOutlined class="h-8 w-8 text-yellow-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-yellow-600">处理中</p>
              <p class="text-2xl font-bold text-yellow-900">8</p>
            </div>
          </div>
        </div>

        <div class="rounded-lg bg-red-50 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <ExclamationCircleOutlined class="h-8 w-8 text-red-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-red-600">紧急</p>
              <p class="text-2xl font-bold text-red-900">3</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 待办列表 -->
      <div class="rounded-lg border border-gray-200">
        <div class="border-b border-gray-200 bg-gray-50 px-6 py-3">
          <h3 class="text-lg font-medium text-gray-900">待办事项</h3>
        </div>
        <div class="divide-y divide-gray-200">
          <div v-for="item in todoItems" :key="item.id" class="p-6">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                  <Tag :color="getPriorityColor(item.priority)">
                    {{ item.priority }}
                  </Tag>
                </div>
                <div>
                  <h4 class="text-sm font-medium text-gray-900">{{ item.title }}</h4>
                  <p class="text-sm text-gray-500">{{ item.description }}</p>
                  <p class="text-xs text-gray-400">{{ item.createTime }}</p>
                </div>
              </div>
              <div class="flex space-x-2">
                <Button size="small" type="primary">处理</Button>
                <Button size="small">查看</Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import {
    Button,
    Tag,
  } from 'ant-design-vue';
  import {
    CheckCircleOutlined,
    ClockCircleOutlined,
    ExclamationCircleOutlined,
    MessageOutlined,
  } from '@ant-design/icons-vue';

  // 待办事项数据
  const todoItems = ref([
    {
      id: 1,
      title: '客户咨询发票开具问题',
      description: '杭州某科技公司咨询如何开具增值税专用发票',
      priority: '高',
      createTime: '2024-01-15 10:30',
    },
    {
      id: 2,
      title: '税务申报截止日期提醒',
      description: '提醒客户本月增值税申报截止日期为25号',
      priority: '中',
      createTime: '2024-01-15 09:15',
    },
    {
      id: 3,
      title: '记账凭证审核异常',
      description: '发现某笔记账凭证科目使用异常，需要人工审核',
      priority: '高',
      createTime: '2024-01-15 08:45',
    },
  ]);

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case '高':
        return 'red';
      case '中':
        return 'orange';
      case '低':
        return 'green';
      default:
        return 'default';
    }
  };
</script>
