<template>
  <div class="h-full p-6">
    <div class="rounded-lg bg-white p-6 shadow-sm">
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">AI客服 - 会话管理</h1>
        <p class="mt-2 text-gray-600">管理客户会话记录和聊天历史</p>
      </div>

      <!-- 搜索和筛选 -->
      <div class="mb-6 flex items-center space-x-4">
        <Input
          v-model:value="searchKeyword"
          placeholder="搜索会话..."
          style="width: 300px"
          allow-clear
        >
          <template #prefix>
            <SearchOutlined />
          </template>
        </Input>
        <Select
          v-model:value="statusFilter"
          placeholder="状态筛选"
          style="width: 150px"
        >
          <Select.Option value="">全部</Select.Option>
          <Select.Option value="active">进行中</Select.Option>
          <Select.Option value="closed">已结束</Select.Option>
        </Select>
      </div>

      <!-- 会话列表 -->
      <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
        <div
          v-for="conversation in filteredConversations"
          :key="conversation.id"
          class="rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-2">
                <h3 class="text-sm font-medium text-gray-900">
                  {{ conversation.customerName }}
                </h3>
                <Tag :color="getStatusColor(conversation.status)">
                  {{ getStatusText(conversation.status) }}
                </Tag>
              </div>
              <p class="mt-1 text-sm text-gray-600">
                {{ conversation.lastMessage }}
              </p>
              <div class="mt-2 flex items-center space-x-4 text-xs text-gray-400">
                <span>{{ conversation.messageCount }} 条消息</span>
                <span>{{ conversation.lastTime }}</span>
              </div>
            </div>
            <div class="flex space-x-2">
              <Button size="small" type="primary" @click="openConversation(conversation)">
                查看
              </Button>
              <Button size="small" @click="closeConversation(conversation)">
                结束
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredConversations.length === 0" class="text-center py-12">
        <Empty description="暂无会话记录" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import {
    Button,
    Empty,
    Input,
    Select,
    Tag,
  } from 'ant-design-vue';
  import { SearchOutlined } from '@ant-design/icons-vue';

  // 搜索和筛选
  const searchKeyword = ref('');
  const statusFilter = ref('');

  // 会话数据
  const conversations = ref([
    {
      id: 1,
      customerName: '杭州科技有限公司',
      lastMessage: '请问增值税专用发票如何开具？',
      messageCount: 15,
      status: 'active',
      lastTime: '2024-01-15 14:30',
    },
    {
      id: 2,
      customerName: '上海贸易公司',
      lastMessage: '谢谢您的帮助，问题已解决',
      messageCount: 8,
      status: 'closed',
      lastTime: '2024-01-15 11:20',
    },
    {
      id: 3,
      customerName: '北京制造企业',
      lastMessage: '关于税务申报的问题...',
      messageCount: 23,
      status: 'active',
      lastTime: '2024-01-15 09:45',
    },
  ]);

  // 过滤后的会话列表
  const filteredConversations = computed(() => {
    let result = conversations.value;

    // 关键词搜索
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase();
      result = result.filter(
        (conv) =>
          conv.customerName.toLowerCase().includes(keyword) ||
          conv.lastMessage.toLowerCase().includes(keyword)
      );
    }

    // 状态筛选
    if (statusFilter.value) {
      result = result.filter((conv) => conv.status === statusFilter.value);
    }

    return result;
  });

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'closed':
        return 'gray';
      default:
        return 'default';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '进行中';
      case 'closed':
        return '已结束';
      default:
        return '未知';
    }
  };

  // 打开会话
  const openConversation = (conversation: any) => {
    console.log('打开会话:', conversation);
    // 这里可以跳转到会话详情页面
  };

  // 结束会话
  const closeConversation = (conversation: any) => {
    console.log('结束会话:', conversation);
    conversation.status = 'closed';
  };
</script>
