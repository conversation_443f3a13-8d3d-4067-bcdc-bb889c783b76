<script setup lang="ts">
  import { ref, watch } from 'vue';

  import { useAccountSubjects } from '#/hooks/jsj-ai/account-book/voucher/index';

  interface Props {
    modelValue?: string;
    showDebugInfo?: boolean;
  }

  interface Emits {
    (e: 'update:modelValue', value: string): void;
    (e: 'change', value: string, option: any): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    showDebugInfo: false,
  });

  const emit = defineEmits<Emits>();

  // 使用新的会计科目 hooks
  const { error, initData, loading, refreshData, subjectOptions } =
    useAccountSubjects();

  const selectedValue = ref(props.modelValue);

  // 搜索过滤函数
  const filterOption = (input: string, option: any) => {
    const searchText = input.toLowerCase();
    return (
      option.label.toLowerCase().includes(searchText) ||
      option.code.toLowerCase().includes(searchText) ||
      option.name.toLowerCase().includes(searchText)
    );
  };

  // 选择变化处理
  const handleChange = (value: any, option?: any) => {
    const stringValue = value ? String(value) : '';
    selectedValue.value = stringValue;
    emit('update:modelValue', stringValue);

    if (option) {
      emit('change', stringValue, option);
    }
  };

  // 监听外部值变化
  watch(
    () => props.modelValue,
    (newValue) => {
      selectedValue.value = newValue;
    },
  );

  // 暴露方法给父组件
  defineExpose({
    initData,
    refreshData,
  });
</script>

<template>
  <div class="account-subject-selector">
    <a-form-item label="会计科目" name="accountSubject">
      <a-select
        v-model:value="selectedValue"
        placeholder="请选择会计科目"
        show-search
        allow-clear
        :loading="loading"
        :options="subjectOptions"
        :filter-option="filterOption"
        @change="handleChange"
      >
        <template #notFoundContent>
          <div v-if="loading">
            <a-spin size="small" />
            加载中...
          </div>
          <div v-else-if="error">
            {{ error }}
          </div>
          <div v-else>暂无数据</div>
        </template>
      </a-select>
    </a-form-item>

    <!-- 调试信息 -->
    <div v-if="showDebugInfo" class="debug-info">
      <h4>调试信息：</h4>
      <p>加载状态: {{ loading }}</p>
      <p>错误信息: {{ error }}</p>
      <p>科目选项数量: {{ subjectOptions.length }}</p>
      <p>选中值: {{ selectedValue }}</p>
      <details>
        <summary>科目选项详情 (前10个)</summary>
        <pre>{{ JSON.stringify(subjectOptions.slice(0, 10), null, 2) }}</pre>
      </details>
    </div>
  </div>
</template>

<style scoped>
  .account-subject-selector {
    width: 100%;
  }

  .debug-info {
    padding: 12px;
    margin-top: 16px;
    font-size: 12px;
    background-color: #f5f5f5;
    border-radius: 4px;
  }

  .debug-info h4 {
    margin: 0 0 8px;
    color: #666;
  }

  .debug-info p {
    margin: 4px 0;
    color: #333;
  }

  .debug-info pre {
    padding: 8px;
    overflow-x: auto;
    font-size: 11px;
    background-color: #fff;
    border-radius: 4px;
  }
</style>
