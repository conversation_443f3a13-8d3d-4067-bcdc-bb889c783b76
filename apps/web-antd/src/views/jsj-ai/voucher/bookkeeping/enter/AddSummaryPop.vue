<script lang="ts" setup>
  import { reactive, ref } from 'vue';

  import { useVbenModal } from '@vben/common-ui';

  import { message } from 'ant-design-vue';

  import { saveSummary } from '#/api/account-book/bookkeeping/index';
  import { useAbstractData } from '#/hooks/jsj-ai/account-book/voucher/index.js';

  import emitter from './usermitt';

  defineOptions({
    name: 'AddSummaryPop',
  });
  const [Modal, ModalApi] = useVbenModal();
  const formRef = ref<any>(null);
  const formState = reactive<{ text: string }>({
    text: '',
  });
  ModalApi.onConfirm = async () => {
    try {
      const data = await formRef.value.validate();
      const res = await saveSummary(data.text);
      if (res.returnCode !== '200') {
        message.error(res.msg);
        return;
      }
      message.success('保存成功');
      emitter.emit('account_voucher_newly_added', {
        id: res.data.id,
        text: data.text,
        type: 'abstract',
      });
      // 获取下新的数据
      const useAbstract = useAbstractData();
      useAbstract.fetchData();
      ModalApi.close();
    } catch {
      // 未验证通过
    }

    // const

    // .then(() => {
    //     console.log('验证通过', formState.text);
    //     // 执行提交逻辑
    // })
    // .catch((error) => {
    //     console.log('验证失败', error);
    // });
  };
</script>
<template>
  <Modal title="新增摘要">
    <div>
      <a-form
        :model="formState"
        name="basic"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        autocomplete="off"
        ref="formRef"
      >
        <a-form-item
          label="摘要内容"
          name="text"
          :rules="[{ required: true, message: '请输入摘要内容' }]"
        >
          <a-input v-model:value="formState.text" />
        </a-form-item>
      </a-form>
    </div>
  </Modal>
</template>
