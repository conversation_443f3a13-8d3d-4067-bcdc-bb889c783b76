<script setup lang="ts">
  import { computed } from 'vue';

  interface Props {
    /**
     * 给ul添加的class name
     */
    classname?: string;
    /**
     * 原始数值，用于判断是否为负数
     */
    originalValue?: number | string;
    type?: string;
    value?: string;
  }
  const props = withDefaults(defineProps<Props>(), {
    classname: '',
    originalValue: undefined,
    type: '',
    value: '',
  });

  const units: string[] = [
    '亿',
    '千',
    '百',
    '十',
    '万',
    '千',
    '百',
    '十',
    '元',
    '角',
    '分',
  ];
  const unitslength: number = units.length;

  // 判断是否为负数
  const isNegative = computed(() => {
    if (props.originalValue !== undefined) {
      return Number(props.originalValue) < 0;
    }
    return false;
  });

  // 计算应该显示的数字和单位
  const displayItems = computed(() => {
    if (props.type === 'head') {
      return units.map((unit) => ({ digit: unit, showDigit: true, unit }));
    }

    if (!props.value) {
      return units.map((unit) => ({ digit: '', showDigit: false, unit }));
    }

    // 找到最高位的非零数字位置（从左到右，即从高位到低位）
    let highestNonZeroUnit = -1;
    for (let _i = 0; _i < units.length; _i++) {
      const valueIndex = unitslength - 1 - _i;
      const digit = props.value[valueIndex] || '';
      if (digit !== '0' && digit !== '') {
        highestNonZeroUnit = _i;
        break;
      }
    }

    // 如果全是0，至少显示元位的0
    if (highestNonZeroUnit === -1) {
      return units.map((unit, i) => ({
        digit: i === 8 ? '0' : '', // 元位显示0
        showDigit: i === 8,
        unit,
      }));
    }

    return units.map((unit, _i) => {
      const valueIndex = unitslength - 1 - _i;
      const digit = props.value[valueIndex] || '';
      // 从最高位非零数字开始显示数字，但保持所有li元素
      const showDigit = _i >= highestNonZeroUnit;
      return { digit, showDigit, unit };
    });
  });
</script>
<template>
  <ul :class="[classname, { 'negative-amount': isNegative }]">
    <li v-for="item in displayItems" :key="item.unit">
      <template v-if="props.type === 'head'">
        <span>{{ item.unit }}</span>
      </template>
      <template v-else>
        <span>
          {{ item.showDigit ? item.digit : '' }}
        </span>
      </template>
    </li>
  </ul>
</template>
<style lang="scss" scoped>
  ul {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0;
    margin: 0;
    pointer-events: none;

    li {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      line-height: 1;
    }

    &.negative-amount {
      font-weight: 500;
      color: #ff4d4f !important; // 负数显示红色

      li {
        font-weight: 500;
        color: #ff4d4f !important; // 确保li元素也是红色
      }
    }
  }
</style>
