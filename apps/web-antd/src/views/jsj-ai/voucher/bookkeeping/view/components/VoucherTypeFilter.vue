<script setup lang="ts">
  import { computed, ref } from 'vue';

  import {
    BankOutlined,
    FileTextOutlined,
    TeamOutlined,
  } from '@ant-design/icons-vue';

  // 凭证类型定义
  export interface VoucherTypeOption {
    color?: string;
    count?: number;
    icon?: any;
    key: string;
    label: string;
  }

  interface Props {
    selectedTypes?: string[];
    showCount?: boolean;
    voucherList: any[];
  }

  interface Emits {
    (e: 'change', selectedTypes: string[]): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    selectedTypes: () => ['all'],
    showCount: true,
  });

  const emit = defineEmits<Emits>();

  // 当前选中的类型
  const selectedTypes = ref<string[]>(props.selectedTypes);

  // 凭证类型选项配置
  const voucherTypeOptions = computed<VoucherTypeOption[]>(() => {
    const typeStats = getVoucherTypeStats();

    return [
      {
        color: '#1890ff',
        count: props.voucherList.length,
        icon: FileTextOutlined,
        key: 'all',
        label: '全部',
      },
      {
        color: '#52c41a',
        count: typeStats['进项发票'] || 0,
        icon: FileTextOutlined,
        key: '进项发票',
        label: '进项发票',
      },
      {
        color: '#fa8c16',
        count: typeStats['销项发票'] || 0,
        icon: FileTextOutlined,
        key: '销项发票',
        label: '销项发票',
      },
      {
        color: '#1890ff',
        count: typeStats['银行回单'] || 0,
        icon: BankOutlined,
        key: '银行回单',
        label: '银行回单',
      },
      {
        color: '#722ed1',
        count: typeStats['工资单'] || 0,
        icon: TeamOutlined,
        key: '工资单',
        label: '工资单',
      },
    ];
  });

  // 统计各类型凭证数量
  function getVoucherTypeStats() {
    const stats: Record<string, number> = {};

    props.voucherList.forEach((voucher) => {
      const sourceType = voucher.source_type || '未知';
      stats[sourceType] = (stats[sourceType] || 0) + 1;
    });

    return stats;
  }

  // 处理类型选择变化
  function handleTypeChange(typeKey: string) {
    if (typeKey === 'all') {
      selectedTypes.value = ['all'];
    } else {
      // 移除 'all' 选项
      const newTypes = selectedTypes.value.filter((t) => t !== 'all');

      if (newTypes.includes(typeKey)) {
        // 如果已选中，则取消选中
        const index = newTypes.indexOf(typeKey);
        newTypes.splice(index, 1);

        // 如果没有选中任何具体类型，则选中 'all'
        selectedTypes.value = newTypes.length === 0 ? ['all'] : newTypes;
      } else {
        // 如果未选中，则添加选中
        newTypes.push(typeKey);
        selectedTypes.value = newTypes;
      }
    }

    emit('change', selectedTypes.value);
  }

  // 检查是否选中
  function isSelected(typeKey: string): boolean {
    return selectedTypes.value.includes(typeKey);
  }

  // 获取选中状态的样式
  function getButtonStyle(option: VoucherTypeOption) {
    const isActive = isSelected(option.key);

    return {
      backgroundColor: isActive ? option.color : 'transparent',
      borderColor: option.color,
      color: isActive ? '#fff' : option.color,
    };
  }
</script>

<template>
  <div class="voucher-type-filter">
    <a-flex align="center" gap="12">
      <!-- <span class="filter-title">凭证类型:</span> -->
      <div class="filter-options">
        <a-button
          v-for="option in voucherTypeOptions"
          :key="option.key"
          size="small"
          :style="getButtonStyle(option)"
          class="type-button"
          @click="handleTypeChange(option.key)"
        >
          <template #icon>
            <component :is="option.icon" />
          </template>
          {{ option.label }}
          <span v-if="showCount" class="count-badge">({{ option.count }})</span>
        </a-button>
      </div>
    </a-flex>
  </div>
</template>

<style scoped>
  /* 响应式设计 */
  @media (max-width: 768px) {
    .filter-options {
      flex-direction: column;
    }

    .type-button {
      justify-content: flex-start;
      width: 100%;
    }
  }

  .voucher-type-filter {
    /* 移除背景和内边距，使其更紧凑 */
  }

  .filter-title {
    flex-shrink: 0;
    font-size: 14px;
    font-weight: 500;
    color: #262626;
    white-space: nowrap;
  }

  .filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }

  .type-button {
    display: flex;
    gap: 4px;
    align-items: center;
    height: 28px;
    padding: 0 8px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .type-button:hover {
    box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
    transform: translateY(-1px);
  }

  .count-badge {
    font-size: 11px;
    opacity: 0.8;
  }
</style>
