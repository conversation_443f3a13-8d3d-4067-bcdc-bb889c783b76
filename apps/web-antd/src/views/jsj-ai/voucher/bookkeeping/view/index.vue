<script setup lang="ts">
  import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';

  import { useUserStore } from '@vben/stores';

  import { message } from 'ant-design-vue';

  // AI凭证相关API
  import {
    deleteVouchers,
    generateVoucherPdf,
    getCurrentVouchers,
    mergeVouchers,
    writeBackVouchers,
  } from '#/api/jsj-ai/api-v2';
  // AI功能相关导入
  import { useCompanySelection } from '#/hooks/jsj-ai/ai-chat/useCompanySelection';
  import { useGlobalLoading } from '#/hooks/useGlobalLoading';
  import { useMonthSelectionStore } from '#/store/modules/month-selection';
  import { useVoucherStore } from '#/store/modules/voucher';
  import { buildVoucherPdfUrl } from '#/utils/file/file-url';
  import OuterBoundary from '#/views/jsj-ai/components/outer-boundary.vue';

  import VirtualVoucherList from './components/VirtualVoucherList.vue';

  // AI功能相关store
  const voucherStore = useVoucherStore();
  const monthSelectionStore = useMonthSelectionStore();
  const userStore = useUserStore();
  const { fetchCompanyNames, selectedCompany, selectedMonth } =
    useCompanySelection();

  // 路由实例
  const router = useRouter();

  // 开发环境标识
  const isDev = import.meta.env.DEV;

  // 基本状态
  const voucherList = ref<any[]>([]);
  const selectAll = ref(false);
  const hasGeneratedVoucher = ref(false);
  const useLoading = useGlobalLoading();

  // 虚拟列表组件引用
  const virtualVoucherListRef = ref();

  // 弹窗状态
  const mergeConfirmVisible = ref(false);
  const deleteConfirmVisible = ref(false);

  // 加载状态
  const mergeLoading = ref(false);
  const deleteLoading = ref(false);
  const printLoading = ref(false);

  // 计算属性
  const selectedVouchers = computed(() => {
    return voucherList.value.filter((voucher) => voucher.isChecked);
  });

  const canMerge = computed(() => {
    const selected = selectedVouchers.value;
    if (selected.length < 2) return false;
    return selected.every((voucher) => voucher.source_type === '银行回单');
  });



  // 全选处理
  const handleSelectAllChange = () => {
    // 先切换全选状态
    selectAll.value = !selectAll.value;

    // 根据新的全选状态更新所有凭证的选中状态
    voucherList.value.forEach((v) => {
      v.isChecked = selectAll.value;
    });
  };

  // 单选处理
  const handleSingleChoice = () => {
    let isAllSelect = true;
    voucherList.value.forEach((v) => {
      if (!v.isChecked) {
        isAllSelect = false;
      }
    });
    selectAll.value = !!isAllSelect;
  };

  // 更新凭证选中状态
  const handleUpdateVoucherChecked = (voucherId: string, checked: boolean) => {
    const voucher = voucherList.value.find((v) => v.id === voucherId);
    if (voucher) {
      voucher.isChecked = checked;
    }
  };

  // 格式化科目显示，包含辅助核算信息
  const formatSubjectWithAuxiliary = (detail: any): string => {
    let subjectName = '';

    // 基础科目信息：科目代码 + 科目名称
    if (detail.account_code && detail.account) {
      subjectName = `${detail.account_code} ${detail.account}`;
    } else if (detail.account) {
      subjectName = detail.account;
    }

    // 添加辅助核算信息 - 支持多种可能的字段名
    const auxiliaryList =
      detail.auxiliary_accounting_list ||
      detail.assistant_accounting ||
      detail.auxiliary ||
      [];

    if (
      auxiliaryList &&
      Array.isArray(auxiliaryList) &&
      auxiliaryList.length > 0
    ) {
      const auxiliaryParts = auxiliaryList
        .filter((aux: any) => aux && (aux.code || aux.name)) // 过滤有效的辅助核算项
        .map((aux: any) => {
          // 格式：辅助核算code + 辅助核算name
          if (aux.code && aux.name) {
            return `${aux.code} ${aux.name}`;
          } else if (aux.name) {
            return aux.name;
          } else if (aux.code) {
            return aux.code;
          }
          return '';
        })
        .filter(Boolean); // 过滤空字符串

      if (auxiliaryParts.length > 0) {
        subjectName += ` ${auxiliaryParts.join(' ')}`;
      }
    }

    return subjectName || '未知科目';
  };

  // 从AI凭证API加载数据
  async function loadVouchersFromAPI() {
    try {
      const companyName = selectedCompany.value;
      const month = monthSelectionStore.getFormattedMonth();

      if (!companyName || !month) {
        message.warning('请先选择公司和月份');
        return;
      }

      useLoading.setShow(true);

      const response = await getCurrentVouchers({
        company_name: companyName,
        month,
      });

      // 检查响应数据结构
      const responseData = response;

      // 根据实际的API响应结构获取items
      let items = [];
      if (responseData && responseData.data && responseData.data.items) {
        items = responseData.data.items;
      } else if (responseData && responseData.items) {
        items = responseData.items;
      } else {
        items = [];
      }

      // 转换AI凭证数据为查看凭证页面的格式
      const apiVouchers = items
        .map((item: any, index: number) => {
          // 确保voucher对象存在
          if (!item.voucher) {
            return null;
          }

          const voucher = item.voucher;
          const details = voucher.details || [];

          if (details.length === 0) {
            message.error('请至少添加一条有效的凭证明细');
            return null;
          }

          return {
            code: `${voucher.type || '记'}-${String(voucher.voucher_num || index + 1).padStart(3, '0')}`,
            confirmed: item.confirmed || false,
            credit: voucher.total_credit || 0,
            date: voucher.record_date || new Date().toISOString().split('T')[0],
            debit: voucher.total_debit || 0,
            detail: details.map((detail: any, detailIndex: number) => ({
              credit: detail.credit || 0,
              debit: detail.debit || 0,
              id: detail.id || detailIndex + 1,
              subjectName: formatSubjectWithAuxiliary(detail),
              summary: detail.summary || '',
            })),
            executor: item.executor || 'system',
            id: voucher.unique_id || `voucher_${index}`,
            inputIsShow: false,
            isChecked: false,
            reviewed: item.confirmed || false,
            source_type: item.source_type || '未知',
            totalAmount: Math.max(
              voucher.total_debit || 0,
              voucher.total_credit || 0,
            ),
            type: voucher.type || '记',
            write_back: voucher.write_back || false,
          };
        })
        .filter(Boolean); // 过滤掉null值

      // 按创建时间或ID降序排列，最新的在前面
      const sortedVouchers = apiVouchers.sort((a: any, b: any) => {
        if (a.date && b.date) {
          return new Date(b.date).getTime() - new Date(a.date).getTime();
        }
        return String(b.id).localeCompare(String(a.id));
      });

      voucherList.value = sortedVouchers;

      if (sortedVouchers.length === 0) {
        message.info('当前公司和月份没有凭证数据');
      } else {
        message.success(`成功加载 ${sortedVouchers.length} 条凭证数据`);
      }
    } catch (error) {
      console.error('加载凭证数据失败:', error);
      message.error('加载凭证数据失败，请重试');
    } finally {
      useLoading.setShow(false);
    }
  }

  // AI功能相关方法
  function applyGeneratedVouchers() {
    message.success('凭证已应用到系统中');
    voucherStore.clearGeneratedVoucherData();
    voucherStore.clearBankReceiptData();
    hasGeneratedVoucher.value = false;
  }

  function clearGeneratedVouchers() {
    voucherStore.clearGeneratedVoucherData();
    voucherStore.clearBankReceiptData();
    hasGeneratedVoucher.value = false;
    message.info('已清除AI生成的凭证');
  }

  // 操作处理函数
  const handleBatchAuditClick = async () => {
    // 获取选中的凭证
    const selectedVoucherItems = voucherList.value.filter(
      (item) => item.isChecked,
    );

    if (selectedVoucherItems.length === 0) {
      message.warning('请先选择要审核的凭证');
      return;
    }

    // 获取选中凭证的ID
    const voucherIds = selectedVoucherItems.map((item) => item.id);

    try {
      useLoading.setShow(true);

      // 获取当前用户信息
      const username = userStore.userInfo?.username || '';

      if (!username) {
        message.error('无法获取用户信息，请重新登录');
        return;
      }

      if (!selectedCompany.value) {
        message.error('请先选择公司');
        return;
      }

      const month = monthSelectionStore.getFormattedMonth();
      if (!month) {
        message.error('请先选择月份');
        return;
      }

      // 调用凭证写入API
      await writeBackVouchers({
        company_name: selectedCompany.value,
        month,
        username,
        voucher_ids: voucherIds,
      });

      message.success(`成功写入 ${selectedVoucherItems.length} 条凭证`);

      // 清除选中状态
      voucherList.value.forEach((item) => {
        item.isChecked = false;
      });
      selectAll.value = false;

      // 重新加载数据
      await loadVouchersFromAPI();
    } catch (error: any) {
      console.error('批量审核失败:', error);
      message.error(`批量审核失败：${error?.message || '未知错误'}`);
    } finally {
      useLoading.setShow(false);
    }
  };

  const handleMergeClick = () => {
    if (!canMerge.value) {
      message.warning('请选择至少2个银行回单类型的凭证进行合并');
      return;
    }
    mergeConfirmVisible.value = true;
  };

  const handleBatchPrintClick = async () => {
    const selectedVoucherItems = voucherList.value.filter(
      (item) => item.isChecked,
    );

    if (selectedVoucherItems.length === 0) {
      message.warning('请先选择要预览的凭证');
      return;
    }

    const companyName = selectedCompany.value;
    const month = monthSelectionStore.getFormattedMonth();

    if (!companyName || !month) {
      message.error('请先选择公司和月份');
      return;
    }

    printLoading.value = true;
    try {
      message.info(`开始批量生成 ${selectedVoucherItems.length} 个凭证预览...`);

      // 逐个生成凭证预览
      for (let i = 0; i < selectedVoucherItems.length; i++) {
        const voucher = selectedVoucherItems[i];
        console.log(
          `生成第 ${i + 1}/${selectedVoucherItems.length} 个凭证预览:`,
          voucher.id,
        );

        try {
          const response = await generateVoucherPdf({
            voucher_id: voucher.id,
          });

          if (response) {
            // 使用统一的PDF文件URL构建工具
            const previewUrl = buildVoucherPdfUrl(response, companyName, month);

            // 在新窗口中打开PDF预览，不触发下载
            window.open(previewUrl, '_blank');

            // 添加延迟避免浏览器阻止多个窗口打开
            if (i < selectedVoucherItems.length - 1) {
              await new Promise((resolve) => setTimeout(resolve, 1000));
            }
          }
        } catch (error) {
          console.error(`凭证 ${voucher.id} 预览生成失败:`, error);
        }
      }

      message.success(
        `批量预览完成，共处理 ${selectedVoucherItems.length} 个凭证`,
      );
    } catch (error: any) {
      console.error('批量生成凭证预览失败:', error);
      message.error('批量预览生成失败，请重试');
    } finally {
      printLoading.value = false;
    }
  };

  const handleDeleteClick = () => {
    const selected = selectedVouchers.value;
    if (selected.length === 0) {
      message.warning('请先选择要删除的凭证');
      return;
    }
    deleteConfirmVisible.value = true;
  };

  const handleVoucherReviewWithId = (voucherId: string) => {
    // 查找对应的凭证数据
    const voucherItem = voucherList.value.find((item) => item.id === voucherId);

    if (voucherItem) {
      // 将凭证数据存储到store中
      voucherStore.setReviewVoucherData({
        code: voucherItem.code,
        confirmed: voucherItem.confirmed,
        credit: voucherItem.credit || 0,
        date: voucherItem.date,
        debit: voucherItem.debit || 0,
        detail: voucherItem.detail,
        executor: voucherItem.executor,
        id: voucherItem.id,
        originalData: voucherItem, // 保存完整的原始数据
        source_type: voucherItem.source_type,
        totalAmount: voucherItem.totalAmount,
        type: voucherItem.type,
      });

      // 使用内部路由跳转到凭证审核页面
      router.push('/bookkeeping/review');
    } else {
      message.error('未找到指定的凭证数据');
    }
  };

  const handlePrintClick = async (voucherId: string) => {
    if (!voucherId) {
      message.error('凭证ID不能为空');
      return;
    }

    const companyName = selectedCompany.value;
    const month = monthSelectionStore.getFormattedMonth();

    if (!companyName || !month) {
      message.error('请先选择公司和月份');
      return;
    }

    printLoading.value = true;
    try {
      console.log('开始生成凭证预览:', voucherId);

      const response = await generateVoucherPdf({
        voucher_id: voucherId,
      });

      console.log('凭证预览响应:', response);

      if (response) {
        // 使用统一的PDF文件URL构建工具
        const previewUrl = buildVoucherPdfUrl(response, companyName, month);

        // 在新窗口中打开PDF预览，不触发下载
        window.open(previewUrl, '_blank');

        message.success('凭证PDF预览已打开');
      } else {
        message.error(response.message || '凭证PDF生成失败');
      }
    } catch (error: any) {
      console.error('生成凭证预览失败:', error);
      message.error(error?.message || '生成凭证预览失败，请重试');
    } finally {
      printLoading.value = false;
    }
  };

  // 弹窗处理
  const confirmMergeVouchers = async () => {
    try {
      mergeLoading.value = true;

      const companyName = selectedCompany.value;
      const month = monthSelectionStore.getFormattedMonth();

      const voucherUniqueIds = selectedVouchers.value.map((voucher) =>
        String(voucher.id),
      );

      console.log('合并凭证请求:', { companyName, month, voucherUniqueIds });

      const response = await mergeVouchers({
        company_name: companyName,
        month,
        voucher_unique_ids: voucherUniqueIds,
      });

      console.log('合并凭证响应:', response);

      const result = response;

      if (result.result === '成功') {
        // 获取合并的凭证数量，兼容不同的数据结构
        let mergedCount = selectedVouchers.value.length;
        try {
          if (result.merged_info?.source_info?.bank_receipt_info) {
            const bankReceiptInfo =
              result.merged_info.source_info.bank_receipt_info;
            if (Array.isArray(bankReceiptInfo)) {
              mergedCount = bankReceiptInfo.length;
            } else if (bankReceiptInfo.merged_voucher_count) {
              mergedCount = bankReceiptInfo.merged_voucher_count;
            }
          }
        } catch (error) {
          console.warn('获取合并数量失败，使用默认值:', error);
        }

        message.success(`成功合并 ${mergedCount} 个凭证`);

        // 清除选择状态
        voucherList.value.forEach((item) => {
          item.isChecked = false;
        });
        selectAll.value = false;

        // 重新加载凭证数据
        await loadVouchersFromAPI();

        // 关闭确认弹窗
        mergeConfirmVisible.value = false;
      } else {
        message.error(result.err_msg || '合并凭证失败');
      }
    } catch (error) {
      console.error('合并凭证失败:', error);
      message.error('合并凭证失败，请重试');
    } finally {
      mergeLoading.value = false;
    }
  };

  const cancelMergeVouchers = () => {
    mergeConfirmVisible.value = false;
  };

  const confirmDeleteVouchers = async () => {
    try {
      deleteLoading.value = true;

      // 获取当前用户信息
      const username = userStore.userInfo?.username || '';

      if (!username) {
        message.error('无法获取用户信息，请重新登录');
        return;
      }

      // 获取选中凭证的ID
      const voucherIds = selectedVouchers.value.map((voucher) =>
        String(voucher.id),
      );

      console.log('删除凭证请求:', { ids: voucherIds, username });

      const response = await deleteVouchers({
        ids: voucherIds,
        username,
      });

      console.log('删除凭证响应:', response);

      // 检查API响应结构
      if (response) {
        // 根据API类型定义，删除响应包含 deleted_count 字段
        const deletedCount =
          response.deleted_count || selectedVouchers.value.length;

        message.success(`成功删除 ${deletedCount} 条凭证`);

        // 清除选中状态
        voucherList.value.forEach((item) => {
          item.isChecked = false;
        });
        selectAll.value = false;

        await loadVouchersFromAPI();
        deleteConfirmVisible.value = false;
      } else {
        message.error('删除凭证失败');
      }
    } catch (error) {
      console.error('删除凭证失败:', error);
      message.error('删除凭证失败，请重试');
    } finally {
      deleteLoading.value = false;
    }
  };

  const cancelDeleteVouchers = () => {
    deleteConfirmVisible.value = false;
  };

  // 监听生成的凭证数据变化
  watch(
    () => voucherStore.generatedVoucherData,
    (newVal) => {
      if (newVal) {
        hasGeneratedVoucher.value = true;
        message.success('AI已生成新的凭证数据');
      }
    },
  );

  // 监听公司和月份变化，重新加载数据
  watch([selectedCompany, selectedMonth], () => {
    if (selectedCompany.value && selectedMonth.value) {
      // 清理之前的审核凭证数据，避免切换公司后显示错误数据
      voucherStore.clearReviewVoucherData();
      loadVouchersFromAPI();
    }
  });

  // 生命周期
  onMounted(async () => {
    try {
      await fetchCompanyNames();
      await new Promise((resolve) => setTimeout(resolve, 100));
      await loadVouchersFromAPI();
    } catch (error) {
      console.error('初始化失败:', error);
      message.error('初始化失败，请刷新页面重试');
    }

    // 添加数据刷新事件监听器
    const handleReloadVoucherData = () => {
      console.log('收到刷新凭证数据事件');
      loadVouchersFromAPI();
    };

    window.addEventListener('reload-voucher-data', handleReloadVoucherData);

    onUnmounted(() => {
      window.removeEventListener(
        'reload-voucher-data',
        handleReloadVoucherData,
      );
    });
  });
</script>

<template>
  <OuterBoundary>
    <div
      class="cont flex flex-1 flex-col rounded-md bg-white"
      style="height: 100%; overflow: hidden"
    >
      <!-- AI生成凭证提示 -->
      <div v-if="hasGeneratedVoucher" class="ai-generated-notice p-2">
        <a-alert
          type="info"
          show-icon
          message="AI已生成凭证数据"
          description="从银行回单生成的凭证数据已添加到凭证列表中。"
        >
          <template #action>
            <div class="ai-generated-actions">
              <a-button
                type="primary"
                size="small"
                @click="applyGeneratedVouchers"
              >
                应用到系统
              </a-button>
              <a-button size="small" @click="clearGeneratedVouchers">
                清除
              </a-button>
            </div>
          </template>
        </a-alert>
      </div>

      <div class="flex flex-1 flex-col" style="height: 100%">
        <!-- 操作按钮 -->
        <div class="p-2">
          <a-flex justify="end">
            <!-- 操作按钮 -->
            <a-space>
              <a-button size="small" @click="loadVouchersFromAPI" v-if="isDev">
                重新加载数据
              </a-button>

              <a-button
                size="small"
                type="primary"
                @click="handleBatchAuditClick"
                :disabled="selectedVouchers.length === 0"
              >
                批量审核
              </a-button>
              <a-button
                size="small"
                type="default"
                @click="handleMergeClick"
                :disabled="!canMerge"
                :loading="mergeLoading"
              >
                合并凭证 ({{ selectedVouchers.length }})
              </a-button>
              <a-button
                size="small"
                type="default"
                @click="handleBatchPrintClick"
                :disabled="selectedVouchers.length === 0"
                :loading="printLoading"
              >
                批量打印 ({{ selectedVouchers.length }})
              </a-button>
              <a-button
                size="small"
                danger
                @click="handleDeleteClick"
                :disabled="selectedVouchers.length === 0"
                :loading="deleteLoading"
              >
                删除凭证 ({{ selectedVouchers.length }})
              </a-button>
            </a-space>
          </a-flex>
        </div>

        <!-- 虚拟凭证列表 -->
        <div class="listcont m-2 flex-1">
          <VirtualVoucherList
            ref="virtualVoucherListRef"
            :voucher-list="voucherList"
            :select-all="selectAll"
            :print-loading="printLoading"
            @single-choice="handleSingleChoice"
            @select-all-change="handleSelectAllChange"
            @voucher-review="handleVoucherReviewWithId"
            @print-click="handlePrintClick"
            @update-voucher-checked="handleUpdateVoucherChecked"
          />
        </div>
      </div>

      <!-- 合并凭证确认弹窗 -->
      <a-modal
        v-model:open="mergeConfirmVisible"
        title="合并凭证确认"
        :confirm-loading="mergeLoading"
        @ok="confirmMergeVouchers"
        @cancel="cancelMergeVouchers"
      >
        <div class="merge-confirm-content">
          <p>
            您确定要合并以下 {{ selectedVouchers.length }} 个银行回单凭证吗？
          </p>
          <p class="merge-warning">
            <strong>注意：</strong>
            合并后的凭证将替换原有的多个凭证，此操作不可撤销。
          </p>
        </div>
      </a-modal>

      <!-- 删除凭证确认弹窗 -->
      <a-modal
        v-model:open="deleteConfirmVisible"
        title="删除凭证确认"
        :confirm-loading="deleteLoading"
        @ok="confirmDeleteVouchers"
        @cancel="cancelDeleteVouchers"
      >
        <div class="delete-confirm-content">
          <p>您确定要删除以下 {{ selectedVouchers.length }} 个凭证吗？</p>
          <p class="delete-warning">
            <strong>警告：</strong>
            删除后的凭证将无法恢复，此操作不可撤销。请谨慎操作！
          </p>
        </div>
      </a-modal>
    </div>
  </OuterBoundary>
</template>

<style lang="scss" scoped>
  /* 容器高度样式 */
  .cont {
    min-height: 0; /* 允许flex子元素收缩 */
  }

  /* AI功能相关样式 */
  .ai-generated-notice {
    .ai-generated-actions {
      display: flex;
      gap: 8px;
    }
  }

  .ai-btn {
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;

    &:hover {
      color: white;
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }
  }

  /* 表格样式 */
  .sticky-row {
    position: sticky;
    top: 0;
    z-index: 1;
    background-color: rgb(250 250 250);

    tr {
      border: solid 1px rgb(233 233 233);
    }

    th {
      outline: rgb(233 233 233) solid 0.5px;
    }
  }

  .listcont {
    position: relative;
    flex: 1;
    min-height: 0; /* 允许flex子元素收缩 */
    overflow: hidden; /* 防止内容溢出 */

    .tablelist {
      width: 100%;
      height: 100%;
      overflow: auto;

      table {
        width: 100%;
        font-size: 12px;
        table-layout: fixed;
        border-collapse: collapse;

        thead {
          font-size: 14px;
          background-color: rgb(250 250 250);
        }

        th,
        td {
          box-sizing: border-box;
          height: 30px !important;
          max-height: 30px;
          padding: 4px 8px;
          line-height: 1.2;
          vertical-align: middle;
          border: solid 1px rgb(233 233 233);
        }

        th {
          font-weight: 600;
        }

        .d1 {
          width: 100%;
          padding: 0;
          margin: 0;
          overflow: hidden;
          line-height: 1.2;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .checkbox-cell {
          background-color: white;
        }

        .voucher-header-cell {
          background-color: #f0f7ff;

          &:hover {
            background-color: #e6f4ff;
          }
        }

        .btns {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;

          .voucher-info {
            display: flex;
            gap: 16px;
            align-items: center;

            .info-item {
              white-space: nowrap;
            }
          }

          .review-btn {
            display: inline-flex;
            align-items: center;
            height: 22px;
            padding: 2px 8px;
            font-size: 11px;
            font-weight: 500;
            color: #1677ff;
            cursor: pointer;
            background-color: #e6f4ff;
            border: 1px solid #91caff;
            border-radius: 3px;
            transition: all 0.2s ease;

            &:hover {
              color: #0958d9;
              background-color: #bae0ff;
              border-color: #69b1ff;
            }

            &:active {
              color: #0958d9;
              background-color: #91caff;
              border-color: #0958d9;
            }
          }
        }
      }
    }
  }

  /* 弹窗样式 */
  .merge-confirm-content,
  .delete-confirm-content {
    .merge-warning,
    .delete-warning {
      padding: 12px;
      margin-top: 16px;
      font-size: 14px;
      border-radius: 6px;
    }

    .merge-warning {
      color: #d46b08;
      background-color: #fff7e6;
      border: 1px solid #ffd591;
    }

    .delete-warning {
      color: #ff4d4f;
      background-color: #fff2f0;
      border: 1px solid #ffccc7;
    }
  }
</style>
