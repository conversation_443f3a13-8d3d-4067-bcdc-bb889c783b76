<script setup lang="ts">
  import { computed, nextTick, onMounted, ref, watch } from 'vue';

  import { useVirtualList } from '@vueuse/core';

  import { useVoucherStore } from '#/store/modules/voucher';

  interface VoucherDetail {
    credit: number;
    debit: number;
    id: number | string;
    subjectName: string;
    summary: string;
  }

  interface VoucherItem {
    code: string;
    credit: number;
    date: string;
    debit: number;
    detail: VoucherDetail[];
    id: string;
    isChecked: boolean;
    source_type: string;
    totalAmount: number;
    write_back: boolean;
  }

  interface Props {
    printLoading: boolean;
    selectAll: boolean;
    voucherList: VoucherItem[];
  }

  interface Emits {
    (e: 'singleChoice'): void;
    (e: 'selectAllChange'): void;
    (e: 'voucherReview', voucherId: string): void;
    (e: 'printClick', voucherId: string): void;
    (e: 'updateVoucherChecked', voucherId: string, checked: boolean): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  // 获取store实例
  const voucherStore = useVoucherStore();

  // 滚动容器引用
  const scrollContainerRef = ref<HTMLElement>();

  // 使用VueUse的虚拟列表
  const { containerProps, wrapperProps, list } = useVirtualList(
    computed(() => props.voucherList),
    {
      itemHeight: (index) => {
        const voucher = props.voucherList[index];
        if (!voucher) return 120;
        // 计算每个凭证的实际高度：头部行 + 明细行 + 合计行
        return (1 + voucher.detail.length + 1) * 30;
      },
      overscan: 5,
    },
  );

  // 保存滚动位置
  const saveScrollPosition = (event?: Event) => {
    const target = event?.target as HTMLElement;
    if (target) {
      const scrollTop = target.scrollTop;
      voucherStore.setVoucherViewScrollPosition(scrollTop);
    }
  };

  // 恢复滚动位置
  const restoreScrollPosition = async () => {
    const savedPosition = voucherStore.getVoucherViewScrollPosition();

    if (savedPosition > 0) {
      await nextTick();

      // 多次尝试获取滚动容器
      const tryRestore = (attempts = 0) => {
        if (attempts > 10) {
          return;
        }

        const container = scrollContainerRef.value;

        if (container) {
          container.scrollTop = savedPosition;
        } else {
          setTimeout(() => tryRestore(attempts + 1), 100);
        }
      };

      // 使用requestAnimationFrame确保DOM更新完成
      requestAnimationFrame(() => {
        tryRestore();
      });
    }
  };

  // 暴露方法给父组件
  defineExpose({
    restoreScrollPosition,
    saveScrollPosition,
  });

  // 格式化数字
  const formatNumber = (num: number): string => {
    if (num === undefined || num === null) {
      return '0.00';
    }
    return num.toLocaleString('zh-CN', {
      maximumFractionDigits: 2,
      minimumFractionDigits: 2,
    });
  };

  // 金额转大写
  const convertCurrency = (money: number): string => {
    const cnNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const cnIntRadice = ['', '拾', '佰', '仟'];
    const cnIntUnits = ['', '万', '亿', '万亿'];
    const cnDecUnits = ['角', '分'];
    const cnInteger = '整';
    const cnIntLast = '元';

    const isNegative = money < 0;
    const absoluteMoney = Math.abs(money);

    let integral = Math.floor(absoluteMoney);
    const decimal = Math.round((absoluteMoney - integral) * 100);
    let result = '';

    if (integral === 0) {
      result = '零元';
      if (decimal > 0) {
        result = '';
      }
    } else {
      // 将数字按万分组处理
      const groups = [];
      while (integral > 0) {
        groups.push(integral % 10_000);
        integral = Math.floor(integral / 10_000);
      }

      for (let groupIndex = groups.length - 1; groupIndex >= 0; groupIndex--) {
        const group = groups[groupIndex];
        if (group === 0) {
          if (
            groupIndex > 0 &&
            groupIndex < groups.length - 1 &&
            result !== ''
          ) {
            result += '零';
          }
          continue;
        }

        let groupStr = '';
        let tempGroup = group;
        let hasNonZero = false;

        // 处理千位
        const qian = Math.floor(tempGroup / 1000);
        if (qian > 0) {
          groupStr += cnNums[qian] + cnIntRadice[3];
          hasNonZero = true;
        }
        tempGroup %= 1000;

        // 处理百位
        const bai = Math.floor(tempGroup / 100);
        if (bai > 0) {
          if (hasNonZero && qian === 0) groupStr += '零';
          groupStr += cnNums[bai] + cnIntRadice[2];
          hasNonZero = true;
        } else if (hasNonZero && tempGroup > 0) {
          groupStr += '零';
        }
        tempGroup %= 100;

        // 处理十位
        const shi = Math.floor(tempGroup / 10);
        if (shi > 0) {
          if (hasNonZero && bai === 0 && qian > 0) groupStr += '零';
          groupStr += cnNums[shi] + cnIntRadice[1];
          hasNonZero = true;
        } else if (hasNonZero && tempGroup > 0) {
          groupStr += '零';
        }
        tempGroup %= 10;

        // 处理个位
        if (tempGroup > 0) {
          if (hasNonZero && shi === 0 && (bai > 0 || qian > 0))
            groupStr += '零';
          groupStr += cnNums[tempGroup];
        }

        // 添加单位（万、亿等）
        if (groupIndex > 0 && groupStr !== '') {
          groupStr += cnIntUnits[groupIndex];
        }

        result += groupStr;
      }

      result += cnIntLast;
    }

    if (decimal > 0) {
      const jiao = Math.floor(decimal / 10);
      const fen = decimal % 10;

      if (jiao > 0 && jiao < cnNums.length) {
        result += cnNums[jiao] + cnDecUnits[0];
      }
      if (fen > 0 && fen < cnNums.length) {
        result += cnNums[fen] + cnDecUnits[1];
      }
    } else {
      result += cnInteger;
    }

    return isNegative ? `负${result}` : result;
  };

  // 处理复选框变化
  const handleCheckboxChange = (voucher: VoucherItem, checked: boolean) => {
    emit('updateVoucherChecked', voucher.id, checked);
    emit('singleChoice');
  };

  // 处理审核点击
  const handleVoucherReview = (voucherId: string) => {
    // 在跳转前保存当前滚动位置
    saveScrollPosition();
    emit('voucherReview', voucherId);
  };

  // 处理打印点击
  const handlePrintClick = (voucherId: string) => {
    emit('printClick', voucherId);
  };

  // 处理全选复选框变化
  const handleSelectAllChange = () => {
    emit('selectAllChange');
  };

  // 监听凭证列表变化，在数据加载完成后恢复滚动位置
  watch(
    () => props.voucherList,
    async (newList) => {
      // 只有在从审核页面返回时才恢复滚动位置
      // 通过检查是否有保存的滚动位置来判断
      const savedPosition = voucherStore.getVoucherViewScrollPosition();
      if (newList && newList.length > 0 && savedPosition > 0) {
        // 延迟恢复滚动位置，确保虚拟列表已经渲染完成
        await nextTick();
        setTimeout(() => {
          restoreScrollPosition();
        }, 500);
      }
    },
    { immediate: false },
  );

  // 组件挂载后设置滚动监听
  onMounted(() => {
    // 获取虚拟列表的滚动容器
    nextTick(() => {
      // 通过 containerProps.ref 获取容器元素
      if (containerProps.ref?.value) {
        scrollContainerRef.value = containerProps.ref.value;
      }
    });

    // 监听返回事件，恢复滚动位置
    const handleReturnFromReview = () => {
      setTimeout(() => {
        restoreScrollPosition();
      }, 300);
    };

    window.addEventListener(
      'voucher-return-from-review',
      handleReturnFromReview,
    );

    // 组件卸载时清理事件监听
    return () => {
      window.removeEventListener(
        'voucher-return-from-review',
        handleReturnFromReview,
      );
    };
  });
</script>

<template>
  <div class="virtual-voucher-list">
    <!-- 表格头部 -->
    <div class="table-header">
      <table class="voucher-table header-table">
        <colgroup>
          <col class="col-checkbox" />
          <col class="col-summary" />
          <col class="col-subject" />
          <col class="col-debit" />
          <col class="col-credit" />
        </colgroup>
        <thead class="sticky-row">
          <tr>
            <th class="text-center">
              <a-checkbox
                :checked="selectAll"
                @change="handleSelectAllChange"
              />
            </th>
            <th class="text-left">摘要</th>
            <th class="text-left">科目</th>
            <th class="text-right">借方金额</th>
            <th class="text-right">贷方金额</th>
          </tr>
        </thead>
      </table>
    </div>

    <!-- 虚拟列表内容 -->
    <div
      v-bind="containerProps"
      style="flex: 1; overflow: auto"
      @scroll="saveScrollPosition"
    >
      <div v-bind="wrapperProps">
        <div
          v-for="{ data: voucher } in list"
          :key="voucher.id"
          class="virtual-row voucher-item"
        >
          <table class="voucher-table content-table">
            <colgroup>
              <col class="col-checkbox" />
              <col class="col-summary" />
              <col class="col-subject" />
              <col class="col-debit" />
              <col class="col-credit" />
            </colgroup>
            <tbody>
              <!-- 凭证头部行 -->
              <tr>
                <td :rowspan="voucher.detail.length + 2" class="checkbox-cell">
                  <div class="h-[100%] text-center">
                    <a-checkbox
                      :checked="voucher.isChecked"
                      @change="
                        (e: any) =>
                          handleCheckboxChange(voucher, e.target.checked)
                      "
                    />
                  </div>
                </td>
                <td colspan="4" class="voucher-header-cell">
                  <div class="btns d1">
                    <div class="voucher-info">
                      <span class="info-item">
                        凭证字号：{{ voucher.code }}
                      </span>
                      <span class="info-item">日期：{{ voucher.date }}</span>
                      <span class="info-item">
                        写入状态：{{ voucher.write_back ? '已写入' : '未写入' }}
                      </span>
                      <span class="info-item">
                        原始凭证类型：{{ voucher.source_type }}
                      </span>
                    </div>
                    <div>
                      <span
                        class="review-btn"
                        @click="handleVoucherReview(voucher.id)"
                      >
                        审核
                      </span>
                      <span
                        class="review-btn"
                        @click="handlePrintClick(voucher.id)"
                        :style="{
                          marginLeft: '8px',
                          opacity: printLoading ? 0.6 : 1,
                        }"
                      >
                        {{ printLoading ? '打印中...' : '打印' }}
                      </span>
                    </div>
                  </div>
                </td>
              </tr>

              <!-- 凭证明细行 -->
              <tr v-for="detailItem in voucher.detail" :key="detailItem.id">
                <td class="text-left">
                  <div class="d1">{{ detailItem.summary }}</div>
                </td>
                <td class="text-left">
                  <div class="d1">{{ detailItem.subjectName }}</div>
                </td>
                <td class="text-right">
                  <div class="d1">
                    <span
                      v-if="
                        detailItem.debit !== 0 &&
                        detailItem.debit !== null &&
                        detailItem.debit !== undefined
                      "
                    >
                      ¥{{ formatNumber(detailItem.debit) }}
                    </span>
                  </div>
                </td>
                <td class="text-right">
                  <div class="d1">
                    <span
                      v-if="
                        detailItem.credit !== 0 &&
                        detailItem.credit !== null &&
                        detailItem.credit !== undefined
                      "
                    >
                      ¥{{ formatNumber(detailItem.credit) }}
                    </span>
                  </div>
                </td>
              </tr>

              <!-- 凭证合计行 -->
              <tr>
                <td class="text-left font-bold">
                  <div class="d1">
                    合计：{{ convertCurrency(voucher.totalAmount || 0) }}
                  </div>
                </td>
                <td class="text-left">
                  <div class="d1"></div>
                </td>
                <td class="text-right font-bold">
                  <div class="d1">¥{{ formatNumber(voucher.debit || 0) }}</div>
                </td>
                <td class="text-right font-bold">
                  <div class="d1">¥{{ formatNumber(voucher.credit || 0) }}</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 空数据提示 -->
    <div v-if="voucherList.length === 0" class="empty mt-10 text-center">
      <div>暂无数据</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .virtual-voucher-list {
    display: flex;
    flex-direction: column;
    height: 100%;

    .table-header {
      flex-shrink: 0;
    }

    .virtual-row {
      &.voucher-item {
        background: white;

        /* 确保每个凭证表格与头部表格对齐 */
        .content-table {
          border-top: 1px solid rgb(233 233 233);
        }

        &:first-child .content-table {
          border-top: none; /* 第一个表格不需要上边框 */
        }
      }
    }
  }

  .voucher-table {
    width: 100%;
    font-size: 12px;
    table-layout: fixed;
    border-collapse: collapse;

    /* 统一列宽定义 */
    .col-checkbox {
      width: 60px;
    }

    .col-summary {
      width: 35%;
    }

    .col-subject {
      width: 25%;
    }

    .col-debit {
      width: 20%;
    }

    .col-credit {
      width: 20%;
    }

    &.header-table {
      border-bottom: 1px solid rgb(233 233 233);
    }

    &.content-table {
      margin-top: -1px; /* 消除与头部表格的间隙 */
    }

    thead {
      font-size: 14px;
      background-color: rgb(250 250 250);
    }

    th,
    td {
      box-sizing: border-box;
      height: 30px !important;
      max-height: 30px;
      padding: 4px 8px;
      line-height: 1.2;
      vertical-align: middle;
      border: solid 1px rgb(233 233 233);
      border-bottom: 0;
    }

    th {
      font-weight: 600;
      border-bottom: 1px solid rgb(233 233 233);
    }

    .d1 {
      width: 100%;
      padding: 0;
      margin: 0;
      overflow: hidden;
      line-height: 1.2;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .checkbox-cell {
      background-color: white;
    }

    .voucher-header-cell {
      background-color: #f0f7ff;

      &:hover {
        background-color: #e6f4ff;
      }
    }

    .btns {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      .voucher-info {
        display: flex;
        gap: 16px;
        align-items: center;

        .info-item {
          white-space: nowrap;
        }
      }

      .review-btn {
        display: inline-flex;
        align-items: center;
        height: 22px;
        padding: 2px 8px;
        font-size: 11px;
        font-weight: 500;
        color: #1677ff;
        cursor: pointer;
        background-color: #e6f4ff;
        border: 1px solid #91caff;
        border-radius: 3px;
        transition: all 0.2s ease;

        &:hover {
          color: #0958d9;
          background-color: #bae0ff;
          border-color: #69b1ff;
        }

        &:active {
          color: #0958d9;
          background-color: #91caff;
          border-color: #0958d9;
        }
      }
    }
  }

  /* 表格头部固定样式 */
  .sticky-row {
    position: sticky;
    top: 0;
    z-index: 1;
    background-color: rgb(250 250 250);

    th {
      outline: rgb(233 233 233) solid 0.5px;
    }
  }
</style>
