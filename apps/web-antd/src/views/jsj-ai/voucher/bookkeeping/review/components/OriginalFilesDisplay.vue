<script setup lang="ts">
  import { computed, onMounted, onUnmounted, ref, watch } from 'vue';

  import {
    DownloadOutlined,
    FileExcelOutlined,
    FileImageOutlined,
    FilePdfOutlined,
    FileTextOutlined,
    LeftOutlined,
    RightOutlined,
  } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';

  import { useCompanySelection } from '#/hooks/jsj-ai/ai-chat/useCompanySelection';
  import { useMonthSelectionStore } from '#/store/modules/month-selection';
  import { buildFileUrl as buildFileUrlUtil } from '#/utils/file/file-url';

  interface FilesData {
    bank_receipt?: any[];
    bank_receipt_info?: any;
    input_invoice?: any[];
    output_invoice?: any[];
    payroll_info?: any[];
  }

  const props = defineProps<{
    filesData: FilesData;
  }>();

  const { selectedCompany } = useCompanySelection();
  const monthSelectionStore = useMonthSelectionStore();

  // 文件导航相关状态
  const currentFileIndex = ref(0);
  const currentCategoryIndex = ref(0);

  // 构建完整的文件URL（使用统一的文件URL构建工具）
  function buildFileUrl(
    filename: string,
    isBankReceipt: boolean = false,
  ): string {
    const companyName = selectedCompany.value;

    if (!companyName) {
      console.warn('公司名称为空，无法构建文件URL');
      return '#';
    }

    // 如果是银行回单的原始文件，需要添加月份路径
    const month = isBankReceipt ? monthSelectionStore.getFormattedMonth() : undefined;

    return buildFileUrlUtil(filename, companyName, isBankReceipt, month);
  }

  // 提取文件URL或文件名（参考AI凭证页面的逻辑）
  function getFileUrl(fileItem: any): string {
    console.log('提取文件URL:', fileItem);

    // 如果有url字段，直接使用
    if (fileItem.url) {
      return fileItem.url;
    }

    // 如果有source_file字段，使用它
    if (fileItem.source_file) {
      return fileItem.source_file;
    }

    // 如果是字符串，直接返回
    if (typeof fileItem === 'string') {
      return fileItem;
    }

    console.warn('无法提取文件URL:', fileItem);
    return '';
  }

  // 获取文件名（参考AI凭证页面的逻辑）
  function getFileName(fileUrlOrItem: any): string {
    console.log('fileUrlOrItem', fileUrlOrItem);
    let url = '';

    url =
      typeof fileUrlOrItem === 'string'
        ? fileUrlOrItem
        : getFileUrl(fileUrlOrItem);

    if (!url || typeof url !== 'string') {
      console.warn('无效的文件URL:', url);
      return '未知文件';
    }

    // 如果是完整URL，提取文件名
    if (url.startsWith('http://') || url.startsWith('https://')) {
      const parts = url.split('/');
      return parts[parts.length - 1] || url;
    }

    // 如果只是文件名，直接返回
    return url;
  }

  // 获取文件图标
  function getFileIcon(filename: string) {
    const ext = filename.toLowerCase().split('.').pop();
    switch (ext) {
      case 'gif':
      case 'jpeg':
      case 'jpg':
      case 'png': {
        return FileImageOutlined;
      }
      case 'pdf': {
        return FilePdfOutlined;
      }
      case 'xls':
      case 'xlsx': {
        return FileExcelOutlined;
      }
      default: {
        return FileTextOutlined;
      }
    }
  }

  // 当前显示的文件组
  const currentFileGroup = computed(() => {
    return allFiles.value[currentCategoryIndex.value] || null;
  });

  // 当前显示的文件
  const currentFile = computed(() => {
    const group = currentFileGroup.value;
    if (!group || !group.items.length) return null;
    return group.items[currentFileIndex.value] || null;
  });

  // 总文件数量
  const totalFiles = computed(() => {
    return allFiles.value.reduce((total, group) => total + group.items.length, 0);
  });

  // 当前文件的全局索引
  const currentGlobalIndex = computed(() => {
    let index = 0;
    for (let i = 0; i < currentCategoryIndex.value; i++) {
      index += allFiles.value[i]?.items.length || 0;
    }
    return index + currentFileIndex.value + 1;
  });

  // 文件导航方法
  function goToPreviousFile() {
    if (currentFileIndex.value > 0) {
      currentFileIndex.value--;
    } else if (currentCategoryIndex.value > 0) {
      currentCategoryIndex.value--;
      const prevGroup = allFiles.value[currentCategoryIndex.value];
      currentFileIndex.value = prevGroup ? prevGroup.items.length - 1 : 0;
    }
  }

  function goToNextFile() {
    const currentGroup = currentFileGroup.value;
    if (!currentGroup) return;

    if (currentFileIndex.value < currentGroup.items.length - 1) {
      currentFileIndex.value++;
    } else if (currentCategoryIndex.value < allFiles.value.length - 1) {
      currentCategoryIndex.value++;
      currentFileIndex.value = 0;
    }
  }

  // 检查是否可以导航
  const canGoPrevious = computed(() => {
    return currentCategoryIndex.value > 0 || currentFileIndex.value > 0;
  });

  const canGoNext = computed(() => {
    const currentGroup = currentFileGroup.value;
    if (!currentGroup) return false;

    return (
      currentFileIndex.value < currentGroup.items.length - 1 ||
      currentCategoryIndex.value < allFiles.value.length - 1
    );
  });

  // 检查文件是否可以直接展示
  function canDisplayInline(filename: string): boolean {
    const ext = filename.toLowerCase().split('.').pop();
    return ['gif', 'jpeg', 'jpg', 'pdf', 'png'].includes(ext || '');
  }

  // 处理图片加载错误
  function handleImageError(event: Event) {
    const target = event.target as HTMLImageElement;
    if (target) {
      target.style.display = 'none';
    }
  }

  // 下载文件
  function downloadFile(
    url: string,
    filename: string,
    isBankReceipt: boolean = false,
  ) {
    try {
      const fullUrl = buildFileUrl(url, isBankReceipt);

      if (fullUrl === '#') {
        message.warning('无法获取文件下载链接');
        return;
      }

      const link = document.createElement('a');
      link.href = fullUrl;
      link.download = filename;
      link.target = '_blank';
      document.body.append(link);
      link.click();
      link.remove();
      message.success('文件下载已开始');
    } catch (error) {
      console.error('下载文件失败:', error);
      message.error('下载文件失败');
    }
  }

  // 提取原始文件列表（参考AI凭证页面的逻辑）
  function extractOriginalFiles(
    dataArray: any[],
    dataType: string = '',
  ): any[] {
    const files: any[] = [];

    dataArray.forEach((item) => {
      // 检查是否有orign_files字段（AI凭证页面的格式）
      if (item.orign_files && Array.isArray(item.orign_files)) {
        files.push(...item.orign_files);
      }
      // 检查是否直接是文件对象
      else if (item.url || item.source_file) {
        files.push(item);
      }
      // 如果是字符串，当作文件名处理
      else if (typeof item === 'string') {
        files.push({ url: item });
      }
      // 对于银行回单等没有orign_files但有业务数据的情况
      else if (dataType === 'bank_receipt' && item.id) {
        // 银行回单可能没有orign_files，但可以根据transaction_id构造文件信息
        const fileName = `${item.transaction_id || item.id}.pdf`;
        files.push({
          bank_data: item, // 保存完整的银行回单数据
          desc: `${item.bank_name || ''} - ${item.summary || ''} - ¥${item.amount || 0}`,
          transaction_id: item.transaction_id,
          url: fileName,
        });
      }
    });

    return files;
  }

  // 计算所有文件列表
  const allFiles = computed(() => {
    console.log('原始文件数据:', props.filesData);

    const files: Array<{
      category: string;
      categoryName: string;
      isBankReceipt?: boolean;
      items: any[];
    }> = [];

    if (props.filesData.input_invoice?.length) {
      console.log('进项发票文件:', props.filesData.input_invoice);
      const extractedFiles = extractOriginalFiles(
        props.filesData.input_invoice,
        'input_invoice',
      );
      if (extractedFiles.length > 0) {
        files.push({
          category: 'input_invoice',
          categoryName: '进项发票',
          items: extractedFiles,
        });
      }
    }

    if (props.filesData.output_invoice?.length) {
      console.log('销项发票文件:', props.filesData.output_invoice);
      const extractedFiles = extractOriginalFiles(
        props.filesData.output_invoice,
        'output_invoice',
      );
      if (extractedFiles.length > 0) {
        files.push({
          category: 'output_invoice',
          categoryName: '销项发票',
          items: extractedFiles,
        });
      }
    }

    // 处理bank_receipt数组格式
    if (props.filesData.bank_receipt?.length) {
      console.log('银行回单文件:', props.filesData.bank_receipt);
      const extractedFiles = extractOriginalFiles(
        props.filesData.bank_receipt,
        'bank_receipt',
      );
      if (extractedFiles.length > 0) {
        files.push({
          category: 'bank_receipt',
          categoryName: '银行回单',
          isBankReceipt: true,
          items: extractedFiles,
        });
      }
    }

    // 处理bank_receipt_info对象格式（getCurrentVouchers接口返回的格式）
    if (props.filesData.bank_receipt_info?.orign_files?.length) {
      console.log('银行回单信息文件:', props.filesData.bank_receipt_info);
      const extractedFiles = extractOriginalFiles(
        props.filesData.bank_receipt_info.orign_files,
        'bank_receipt_info',
      );
      if (extractedFiles.length > 0) {
        files.push({
          category: 'bank_receipt_info',
          categoryName: '银行回单',
          isBankReceipt: true,
          items: extractedFiles,
        });
      }
    }

    if (props.filesData.payroll_info?.length) {
      console.log('工资单文件:', props.filesData.payroll_info);
      const extractedFiles = extractOriginalFiles(
        props.filesData.payroll_info,
        'payroll_info',
      );
      if (extractedFiles.length > 0) {
        files.push({
          category: 'payroll_info',
          categoryName: '工资单',
          items: extractedFiles,
        });
      }
    }

    console.log('处理后的文件列表:', files);
    return files;
  });

  // 键盘导航支持
  function handleKeydown(event: KeyboardEvent) {
    if (event.target && (event.target as HTMLElement).tagName === 'INPUT') {
      return; // 如果焦点在输入框中，不处理键盘事件
    }

    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault();
        goToPreviousFile();
        break;
      case 'ArrowRight':
        event.preventDefault();
        goToNextFile();
        break;
    }
  }

  // 监听文件列表变化，重置导航状态
  watch(
    allFiles,
    (newFiles) => {
      if (newFiles && newFiles.length > 0) {
        currentCategoryIndex.value = 0;
        currentFileIndex.value = 0;
        console.log('文件列表更新，重置导航状态');
      }
    },
    { immediate: true },
  );

  // 组件挂载时添加键盘事件监听
  onMounted(() => {
    document.addEventListener('keydown', handleKeydown);
  });

  // 组件卸载时移除键盘事件监听
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown);
  });
</script>

<template>
  <div class="original-files-display">
    <div v-if="allFiles.length === 0" class="empty-files">
      <a-empty description="暂无原始文件" />
    </div>

    <div v-else class="files-container">
      <!-- 文件导航栏 -->
      <div class="file-navigation">
        <div class="nav-info">
          <a-tag color="blue">
            {{ currentFileGroup?.categoryName || '未知类型' }}
          </a-tag>
          <span class="file-counter">
            {{ currentGlobalIndex }} / {{ totalFiles }}
          </span>
          <a-tooltip title="使用左右箭头键快速切换文件">
            <span class="keyboard-hint">⌨️ ← →</span>
          </a-tooltip>
        </div>

        <div class="nav-controls">
          <a-button
            :disabled="!canGoPrevious"
            size="small"
            @click="goToPreviousFile"
          >
            <template #icon>
              <LeftOutlined />
            </template>
            上一张
          </a-button>

          <a-button
            :disabled="!canGoNext"
            size="small"
            @click="goToNextFile"
          >
            下一张
            <template #icon>
              <RightOutlined />
            </template>
          </a-button>
        </div>
      </div>

      <!-- 当前文件显示区域 -->
      <div v-if="currentFile" class="current-file-display">
        <!-- 文件信息头部 -->
        <div class="file-header">
          <div class="file-info">
            <div class="file-icon">
              <component :is="getFileIcon(getFileName(currentFile))" />
            </div>
            <div class="file-details">
              <div class="file-name" :title="getFileName(currentFile)">
                {{ getFileName(currentFile) }}
              </div>
              <div v-if="currentFile.desc" class="file-desc" :title="currentFile.desc">
                {{ currentFile.desc }}
              </div>
            </div>
          </div>

          <div class="file-actions">
            <a-button
              type="text"
              size="small"
              @click="
                downloadFile(
                  getFileUrl(currentFile),
                  getFileName(currentFile),
                  currentFileGroup?.isBankReceipt,
                )
              "
            >
              <template #icon>
                <DownloadOutlined />
              </template>
              下载
            </a-button>
          </div>
        </div>

        <!-- 文件内容展示区域 -->
        <div class="file-content">
          <div
            v-if="canDisplayInline(getFileName(currentFile))"
            class="file-display"
          >
            <!-- PDF文件展示 -->
            <iframe
              v-if="getFileName(currentFile).toLowerCase().endsWith('.pdf')"
              :src="buildFileUrl(getFileUrl(currentFile), currentFileGroup?.isBankReceipt)"
              class="file-iframe pdf-iframe"
              frameborder="0"
            ></iframe>
            <!-- 图片文件展示 -->
            <img
              v-else-if="
                ['jpg', 'jpeg', 'png', 'gif'].includes(
                  getFileName(currentFile).toLowerCase().split('.').pop() || '',
                )
              "
              :src="buildFileUrl(getFileUrl(currentFile), currentFileGroup?.isBankReceipt)"
              :alt="getFileName(currentFile)"
              class="file-image"
              @error="handleImageError"
            />
          </div>
          <div v-else class="unsupported-file">
            <a-empty description="该文件类型不支持在线预览，请下载查看" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>


  // 响应式设计
  @media (max-width: 768px) {
    .file-navigation {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .nav-info {
        justify-content: center;
      }

      .nav-controls {
        justify-content: center;
      }
    }

    .file-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .file-info {
        justify-content: center;
      }

      .file-actions {
        justify-content: center;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .file-navigation {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .nav-info {
        justify-content: center;
      }

      .nav-controls {
        justify-content: center;
      }
    }

    .file-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .file-info {
        justify-content: center;
      }

      .file-actions {
        justify-content: center;
      }
    }
  }

  .original-files-display {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  .empty-files {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
  }

  .files-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  .file-navigation {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border: 1px solid #e8e8e8;
    border-radius: 4px;

    .nav-info {
      display: flex;
      gap: 8px;
      align-items: center;

      .file-counter {
        font-size: 13px;
        font-weight: 500;
        color: #262626;
      }

      .keyboard-hint {
        padding: 1px 4px;
        font-size: 11px;
        color: #8c8c8c;
        cursor: help;
        background: #f0f0f0;
        border-radius: 2px;
      }
    }

    .nav-controls {
      display: flex;
      gap: 6px;
    }
  }

  .current-file-display {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  .file-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;

    .file-info {
      display: flex;
      flex: 1;
      gap: 12px;
      align-items: center;
      min-width: 0;

      .file-icon {
        flex-shrink: 0;
        font-size: 18px;
        color: #1890ff;
      }

      .file-details {
        flex: 1;
        min-width: 0;

        .file-name {
          margin-bottom: 4px;
          overflow: hidden;
          font-size: 14px;
          font-weight: 500;
          color: #262626;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .file-desc {
          overflow: hidden;
          font-size: 12px;
          color: #8c8c8c;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .file-actions {
      display: flex;
      flex-shrink: 0;
      gap: 8px;
    }
  }

  .file-content {
    flex: 1;
    padding: 16px;
    overflow: auto;
    background: #fff;
  }

  .file-display {
    width: 100%;
    height: 100%;

    .file-iframe {
      width: 100%;
      height: 100%;
      min-height: 400px;
      border: 1px solid #e8e8e8;
      border-radius: 4px;

      &.pdf-iframe {
        min-height: 450px;
      }
    }

    .file-image {
      max-width: 100%;
      height: auto;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    }
  }

  .unsupported-file {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    background: #fafafa;
    border-radius: 4px;
  }
</style>
