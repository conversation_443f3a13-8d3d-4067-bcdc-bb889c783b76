<script setup lang="ts">
  import { computed, ref, watch } from 'vue';

  interface AuxiliaryOption {
    code?: string;
    id: string;
    name: string;
  }

  interface Props {
    modelValue?: string;
    options?: AuxiliaryOption[];
    placeholder?: string;
    type?: 'customer' | 'department' | 'employee' | 'supplier';
  }

  interface Emits {
    (e: 'update:modelValue', value: string): void;
    (e: 'change', value: string, option: AuxiliaryOption): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    options: () => [],
    placeholder: '请选择辅助核算',
    type: 'supplier',
  });

  const emit = defineEmits<Emits>();

  const selectedValue = ref(props.modelValue);

  const fieldNames = {
    label: 'name',
    value: 'id',
  };

  const options = computed(() => {
    return props.options.map((item) => ({
      ...item,
      label: item.name,
      value: item.id,
    }));
  });

  const filterOption = (input: string, option: any) => {
    return (
      option.name.toLowerCase().includes(input.toLowerCase()) ||
      (option.code && option.code.toLowerCase().includes(input.toLowerCase()))
    );
  };

  const handleChange = (value: string) => {
    selectedValue.value = value;
    emit('update:modelValue', value);

    const selectedOption = props.options.find((item) => item.id === value);
    if (selectedOption) {
      emit('change', value, selectedOption);
    }
  };

  watch(
    () => props.modelValue,
    (newValue) => {
      selectedValue.value = newValue;
    },
  );
</script>

<template>
  <a-select
    v-model:value="selectedValue"
    :placeholder="placeholder"
    :options="options"
    :field-names="fieldNames"
    show-search
    allow-clear
    :filter-option="filterOption"
    @change="handleChange"
  />
</template>
