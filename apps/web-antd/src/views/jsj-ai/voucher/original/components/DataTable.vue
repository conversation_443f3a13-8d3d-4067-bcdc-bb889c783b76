<script setup lang="ts">
  import type { TableColumn } from '../data';

  import { computed, ref } from 'vue';

  interface Props {
    columns: TableColumn[];
    dataSource: any[];
    loading?: boolean;
    onResizeColumn?: (width: number, column: any) => void;
    pagination?: {
      current: number;
      pageSize: number;
      total: number;
    };
    rowSelection?: any;
    tableKey?: string;
  }

  interface Emits {
    (e: 'paginationChange', page: number, pageSize: number): void;
    (e: 'pageSizeChange', current: number, size: number): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false,
    tableKey: 'data-table',
  });

  const emit = defineEmits<Emits>();

  // 表格引用
  const tableRef = ref();

  // 表格滚动配置
  const tableScrollConfig = computed(() => ({
    y: 'calc(100vh - 350px)', // 设置垂直滚动高度
  }));

  // 分页后的表格数据
  const paginatedTableData = computed(() => {
    if (!props.pagination) return props.dataSource;

    const start = (props.pagination.current - 1) * props.pagination.pageSize;
    const end = start + props.pagination.pageSize;
    return props.dataSource.slice(start, end);
  });

  // 处理列宽调整
  const handleResizeColumn = (w: number, col: any) => {
    // 必须更新列宽，否则拖拽不会生效
    col.width = w;

    // 调用父组件的回调
    if (props.onResizeColumn) {
      props.onResizeColumn(w, col);
    }
  };

  // 分页处理方法
  const handlePaginationChange = (page: number, pageSize: number) => {
    emit('paginationChange', page, pageSize);
  };

  const handlePageSizeChange = (current: number, size: number) => {
    emit('pageSizeChange', current, size);
  };
</script>

<template>
  <div class="data-table-container">
    <!-- 表格内容区域 -->
    <div class="data-table-wrap">
      <a-table
        ref="tableRef"
        :key="tableKey"
        :columns="columns"
        :data-source="paginatedTableData"
        :loading="loading"
        :pagination="false"
        :row-selection="rowSelection"
        :scroll="tableScrollConfig"
        row-key="_id"
        size="small"
        @resize-column="handleResizeColumn"
      />
    </div>

    <!-- 底部分页区域 -->
    <div v-if="pagination" class="data-table-footer">
      <div class="pagination-wrap">
        <div class="pagination-info">
          当前第 {{ pagination.current }} 到
          {{
            Math.min(pagination.current * pagination.pageSize, pagination.total)
          }}
          条，总计 {{ pagination.total }} 条
        </div>
        <a-pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :page-size-options="['20', '50', '100']"
          size="small"
          @change="handlePaginationChange"
          @show-size-change="handlePageSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
  /* 表格容器样式 */
  .data-table-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    background: #fff;
  }

  .data-table-wrap {
    flex: 1;
    height: calc(100vh - 350px);
    min-height: 400px;
    overflow: hidden;
    background: #fff;
  }

  /* 底部分页区域样式 */
  .data-table-footer {
    flex-shrink: 0;
    padding: 8px 16px;
    background: #fff;
    border-top: 1px solid #f0f0f0;
  }

  .pagination-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 40px;
    padding: 4px 0;
  }

  .pagination-info {
    font-size: 12px;
    color: #666;
  }

  /* 表格样式 */
  :deep(.ant-table) {
    overflow: hidden;
    font-size: 12px;
    border-radius: 0;

    .ant-table-thead > tr > th {
      box-sizing: border-box;
      height: 32px;
      padding: 6px 8px !important;
      font-size: 12px;
      font-weight: 600;
      color: #333;
      white-space: nowrap;
      background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
      border-bottom: 2px solid #e8eaed;
    }

    .ant-table-tbody > tr > td {
      box-sizing: border-box;
      min-height: 30px;
      padding: 4px 8px;
      font-size: 12px;
      line-height: 1.4;
      word-wrap: break-word;
      white-space: normal;
      border-bottom: 1px solid #f0f0f0;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f8f9ff;
    }

    .ant-table-tbody > tr {
      height: 30px;
    }
  }

  :deep(.ant-pagination-options) {
    margin-left: 8px !important;
  }
</style>
