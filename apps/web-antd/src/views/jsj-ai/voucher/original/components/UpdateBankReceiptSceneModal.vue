<script setup lang="ts">
  import type { FormInstance } from 'ant-design-vue';

  import { computed, ref, watch } from 'vue';

  import {
    useOriginalVoucherSceneOptions,
    useSceneDetails,
  } from '#/hooks/jsj-ai/configuration/useSceneOptions';

  interface UpdateBankReceiptSceneFormData {
    br_account_name: string;
    br_counterparty_account_name: string;
    br_currency: string;
    br_note: string;
    br_summary: string;
    // 其他字段保持默认值，不在表单中显示
    br_type: string;
    scene: string;
  }

  interface Props {
    activeTab: string;
    companyName: string;
    loading?: boolean;
    selectedCount: number;
    visible: boolean;
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void;
    (e: 'confirm', data: UpdateBankReceiptSceneFormData): void;
    (e: 'cancel'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    activeTab: 'bank',
    loading: false,
  });

  const emit = defineEmits<Emits>();

  const formRef = ref<FormInstance>();

  // 默认值配置
  const defaultValues = {
    br_account_name: '',
    br_counterparty_account_name: '',
    br_currency: 'CNY',
    br_note: '',
    br_summary: '',
    br_type: '',
  };

  const formData = ref<UpdateBankReceiptSceneFormData>({
    scene: '',
    ...defaultValues,
  });

  // 使用场景选择hook
  const companyNameRef = {
    value: computed(() => props.companyName),
  };
  const activeTabRef = {
    value: computed(() => props.activeTab),
  };
  const {
    fetchSceneOptions,
    loading: loadingScenes,
    sceneOptions,
  } = useOriginalVoucherSceneOptions(companyNameRef, activeTabRef);

  // 使用场景详情hook，指定银行回单类型
  const { fetchSceneDetails, getSceneDetails } = useSceneDetails(
    companyNameRef,
    '银行回单',
  );

  // 当前选中场景的详情
  const currentSceneDetails = ref<any[]>([]);

  // 监听visible变化，重置表单并获取场景数据
  watch(
    () => props.visible,
    (newVisible) => {
      if (newVisible) {
        resetForm();
        // 当模态框打开时，主动获取场景数据
        if (props.companyName) {
          fetchSceneOptions();
          fetchSceneDetails();
        }
      }
    },
  );

  // 监听场景选择变化，获取对应的分录详情
  watch(
    () => formData.value.scene,
    (newScene) => {
      if (newScene) {
        const details = getSceneDetails(newScene);
        currentSceneDetails.value = details;
        console.log('选中银行回单场景分录详情:', details);
      } else {
        currentSceneDetails.value = [];
      }
    },
  );

  function resetForm() {
    formData.value = {
      scene: '',
      ...defaultValues,
    };
    currentSceneDetails.value = [];
    formRef.value?.clearValidate();
  }

  async function handleConfirm() {
    try {
      await formRef.value?.validate();
      emit('confirm', { ...formData.value });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  }

  function handleCancel() {
    formData.value.scene = '';
    emit('cancel');
    emit('update:visible', false);
  }
</script>

<template>
  <a-modal
    :open="visible"
    title="批量更新银行回单场景"
    width="500px"
    :confirm-loading="loading"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="update-scene-form">
      <a-form
        ref="formRef"
        :model="formData"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item
          label="场景名称"
          name="scene"
          :rules="[{ required: true, message: '请选择场景名称' }]"
        >
          <a-select
            v-model:value="formData.scene"
            :loading="loadingScenes"
            :options="sceneOptions"
            placeholder="请选择场景名称"
            show-search
            :filter-option="
              (input: string, option: any) =>
                option?.label?.toLowerCase().includes(input.toLowerCase())
            "
          />
        </a-form-item>
      </a-form>

      <!-- 分录详情展示 -->
      <div v-if="currentSceneDetails.length > 0" class="scene-details">
        <div class="details-title">分录详情</div>
        <div class="details-list">
          <div
            v-for="(detail, index) in currentSceneDetails"
            :key="index"
            class="detail-item"
          >
            <div class="detail-row">
              <span class="detail-label">摘要:</span>
              <span class="detail-value">{{ detail.summary || '-' }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">科目:</span>
              <span class="detail-value">{{ detail.subject || '-' }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">方向:</span>
              <span
                class="detail-value direction-badge"
                :class="
                  detail.direction === '借'
                    ? 'direction-debit'
                    : 'direction-credit'
                "
              >
                {{ detail.direction || '-' }}
              </span>
            </div>
            <div class="detail-row">
              <span class="detail-label">数据源:</span>
              <span class="detail-value">{{ detail.source || '-' }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="selected-info">
        <a-alert
          :message="`已选择 ${selectedCount} 条银行回单记录`"
          type="info"
          show-icon
        />
      </div>
    </div>
  </a-modal>
</template>

<style scoped>
  .update-scene-form {
    padding: 16px 0;
  }

  .scene-details {
    padding: 12px;
    margin-top: 16px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
  }

  .details-title {
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
  }

  .details-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .detail-item {
    padding: 8px 12px;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
  }

  .detail-row {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
  }

  .detail-row:last-child {
    margin-bottom: 0;
  }

  .detail-label {
    min-width: 60px;
    font-size: 12px;
    font-weight: 500;
    color: #6c757d;
  }

  .detail-value {
    margin-left: 8px;
    font-size: 12px;
    color: #212529;
  }

  .direction-badge {
    padding: 2px 6px;
    font-size: 11px;
    font-weight: 500;
    border-radius: 3px;
  }

  .direction-debit {
    color: #1976d2;
    background-color: #e3f2fd;
  }

  .direction-credit {
    color: #7b1fa2;
    background-color: #f3e5f5;
  }

  .selected-info {
    padding-top: 16px;
    margin-top: 16px;
    border-top: 1px solid #f0f0f0;
  }

  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }
</style>
