<script setup lang="ts">
  import { computed } from 'vue';

  interface Props {
    activeTab: string;
    selectedCount?: number;
  }

  interface Emits {
    (e: 'search'): void;
    (e: 'reset'): void;
    (e: 'syncData'): void;
    (e: 'bankReceiptSync'): void;
    (e: 'aiAccounting'): void;
    (e: 'updateScene'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    selectedCount: 0,
  });

  const emit = defineEmits<Emits>();

  // 处理各种按钮点击事件
  const handleSearch = () => emit('search');
  const handleReset = () => emit('reset');
  const handleSyncData = () => emit('syncData');
  const handleBankReceiptSync = () => emit('bankReceiptSync');
  const handleAiAccounting = () => emit('aiAccounting');
  const handleUpdateScene = () => emit('updateScene');

  // 判断是否显示各种按钮
  const showSyncDataButton = computed(
    () => props.activeTab === 'input' || props.activeTab === 'output',
  );

  const showBankReceiptSyncButton = computed(() => props.activeTab === 'bank');

  const showAiAccountingButton = computed(
    () => props.activeTab === 'input' || props.activeTab === 'output',
  );

  const showUpdateSceneButton = computed(
    () =>
      props.activeTab === 'input' ||
      props.activeTab === 'output' ||
      props.activeTab === 'bank',
  );
</script>

<template>
  <div class="action-buttons">
    <a-button type="primary" size="small" @click="handleSearch">查询</a-button>

    <a-button class="reset-btn" size="small" @click="handleReset">
      重置
    </a-button>

    <a-button v-if="showSyncDataButton" size="small" @click="handleSyncData">
      同步数据
    </a-button>

    <a-button
      v-if="showBankReceiptSyncButton"
      size="small"
      @click="handleBankReceiptSync"
    >
      同步银行回单
    </a-button>

    <a-button
      v-if="showAiAccountingButton"
      class="ai-btn"
      size="small"
      @click="handleAiAccounting"
    >
      AI记账
    </a-button>

    <a-button
      v-if="showUpdateSceneButton"
      size="small"
      @click="handleUpdateScene"
    >
      更新场景
      <span v-if="selectedCount > 0" class="selected-count">
        ({{ selectedCount }})
      </span>
    </a-button>
  </div>
</template>

<style scoped>
  .action-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: flex-end;
    padding-top: 4px;
  }

  .selected-count {
    margin-left: 4px;
    font-size: 11px;
    color: #666;
  }

  /* 按钮基础样式 */
  :deep(.ant-btn) {
    height: 28px;
    padding: 0 12px;
    font-size: 13px;
    border-radius: 6px;

    /* 主按钮样式 */
    &.ant-btn-primary {
      color: #fff;
      background: #1890ff;
      border-color: #1890ff;

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }

      &:active {
        background: #096dd9;
        border-color: #096dd9;
      }
    }

    /* AI按钮特殊样式 */
    &.ai-btn {
      color: #fff !important;
      background: #722ed1 !important;
      border-color: #722ed1 !important;

      &:hover {
        background: #9254de !important;
        border-color: #9254de !important;
      }

      &:active {
        background: #531dab !important;
        border-color: #531dab !important;
      }
    }

    /* 次要按钮样式 */
    &.ant-btn-default {
      color: #666;
      background: #fff;
      border: 1px solid #d9d9d9;

      &:hover {
        color: #1890ff;
        border-color: #1890ff;
      }

      &:active {
        color: #096dd9;
        border-color: #096dd9;
      }
    }

    /* 禁用状态样式 */
    &[disabled] {
      color: rgb(0 0 0 / 25%) !important;
      background: #f5f5f5 !important;
      border-color: #d9d9d9 !important;
    }

    /* 小尺寸按钮样式调整 */
    &.ant-btn-sm {
      height: 28px;
      padding: 0 12px;
      font-size: 13px;
    }
  }
</style>
