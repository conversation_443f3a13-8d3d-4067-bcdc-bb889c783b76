<script setup lang="ts">
  import { computed, watch } from 'vue';

  interface FormField {
    component: string;
    componentProps?: Record<string, any>;
    defaultValue?: any;
    fieldName: string;
    label: string;
    required?: boolean;
  }

  interface Props {
    modelValue: Record<string, any>;
    schema: FormField[];
  }

  interface Emits {
    (e: 'update:modelValue', value: Record<string, any>): void;
    (e: 'submit', value: Record<string, any>): void;
    (e: 'fieldChange', fieldName: string, value: any): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  // 表单数据
  const formData = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  });

  // 监听schema变化，初始化表单数据
  watch(
    () => props.schema,
    (newSchema) => {
      if (newSchema.length === 0) return;

      const newFormData: Record<string, any> = { ...formData.value };
      let hasChanges = false;

      newSchema.forEach((field) => {
        if (
          newFormData[field.fieldName] === undefined &&
          field.defaultValue !== undefined
        ) {
          newFormData[field.fieldName] = field.defaultValue;
          hasChanges = true;
        }
      });

      if (hasChanges) {
        emit('update:modelValue', newFormData);
      }
    },
    { immediate: true },
  );

  // 获取必填提示信息
  const getRequiredMessage = (field: FormField) => {
    if (field.component === 'Select') {
      return `选择${field.label}`;
    }
    return `输入${field.label}`;
  };

  // 处理字段变化
  const handleFieldChange = (fieldName: string, value: any) => {
    // 更新表单数据
    const newFormData = { ...formData.value };
    newFormData[fieldName] = value;
    emit('update:modelValue', newFormData);

    // 发出字段变化事件
    emit('fieldChange', fieldName, value);
  };

  // 表单提交
  const handleSubmit = () => {
    emit('submit', formData.value);
  };

  // 格式化日期范围工具提示
  const formatDateRangeTooltip = (dateRange: any) => {
    if (!dateRange || !Array.isArray(dateRange) || dateRange.length !== 2) {
      return '';
    }
    const [start, end] = dateRange;
    if (!start || !end) return '';
    return `${start} 至 ${end}`;
  };
</script>

<template>
  <div class="filter-form">
    <a-form
      :model="formData"
      layout="inline"
      class="filter-form-content"
      @finish="handleSubmit"
    >
      <div class="form-grid">
        <template v-for="field in schema" :key="field.fieldName">
          <a-form-item
            :label="field.label"
            :name="field.fieldName"
            :rules="
              field.required
                ? [
                    {
                      required: true,
                      message: `请${getRequiredMessage(field)}`,
                    },
                  ]
                : undefined
            "
            class="form-item"
          >
            <!-- 输入框 -->
            <a-input
              v-if="field.component === 'Input'"
              v-model:value="formData[field.fieldName]"
              v-bind="field.componentProps"
              class="form-control"
              size="small"
            />

            <!-- 选择器 -->
            <a-select
              v-else-if="field.component === 'Select'"
              v-model:value="formData[field.fieldName]"
              v-bind="field.componentProps"
              class="form-control"
              size="small"
              @change="(value) => handleFieldChange(field.fieldName, value)"
            />

            <!-- 日期范围选择器 -->
            <a-range-picker
              v-else-if="field.component === 'RangePicker'"
              v-model:value="formData[field.fieldName]"
              v-bind="field.componentProps"
              class="form-control range-picker"
              size="small"
              :title="formatDateRangeTooltip(formData[field.fieldName])"
            />

            <!-- 月份选择器 -->
            <a-date-picker
              v-else-if="field.component === 'MonthPicker'"
              v-model:value="formData[field.fieldName]"
              v-bind="field.componentProps"
              picker="month"
              class="form-control"
              size="small"
            />
          </a-form-item>
        </template>
      </div>
    </a-form>
  </div>
</template>

<style scoped>


  /* 响应式布局优化 */

  /* 超大屏幕：最多3列 */
  @media (min-width: 1600px) {
    .form-grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 16px 20px;
    }
  }

  /* 大屏幕：最多3列 */
  @media (max-width: 1599px) and (min-width: 1200px) {
    .form-grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 14px 18px;
    }
  }

  /* 中等屏幕：2列布局 */
  @media (max-width: 1199px) and (min-width: 768px) {
    .form-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 12px 16px;
    }
  }

  /* 小屏幕：单列布局 */
  @media (max-width: 767px) {
    .form-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }

  /* 响应式日期选择器宽度设置 - 确保日期文本完整显示 */
  @media (min-width: 1200px) {
    :deep(.ant-picker-range) {
      min-width: 280px !important; /* 大屏幕下提供充足空间 */
    }
  }

  @media (max-width: 1199px) and (min-width: 768px) {
    :deep(.ant-picker-range) {
      min-width: 260px !important; /* 中等屏幕下保证日期完整显示 */
    }
  }

  @media (max-width: 767px) {
    :deep(.ant-picker-range) {
      min-width: 240px !important; /* 小屏幕下的最小宽度 */
    }

    :deep(.ant-picker-range .ant-picker-range-separator) {
      padding: 0 2px; /* 移动端减小分隔符间距 */
    }
  }

  /* 在极小屏幕上进一步优化 */
  @media (max-width: 480px) {
    :deep(.ant-picker-range) {
      min-width: 220px !important;
    }

    :deep(.ant-picker-range .ant-picker-input > input) {
      padding: 3px 6px !important;
      font-size: 13px !important;
    }

    :deep(.ant-picker-range .ant-picker-range-separator) {
      padding: 0 1px;
      font-size: 12px;
    }
  }



  /* 基础布局样式 */
  .filter-form {
    width: 100%;
  }

  .filter-form-content {
    width: 100%;
  }

  .form-grid {
    display: grid;

    /* 使用更紧凑的网格布局 */
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 8px 12px;
    width: 100%;
    max-width: 100%;
  }

  .form-item {
    width: 100%;
    margin-bottom: 0 !important;
  }

  .form-control {
    width: 100% !important;
    min-width: 0; /* 允许收缩到容器大小 */
    max-width: 100%; /* 防止超出容器 */
  }

  /* 日期范围选择器特殊样式 */
  .range-picker {
    min-width: 0 !important; /* 移除固定最小宽度 */
    max-width: 100% !important; /* 确保不超出容器 */
  }



  /* 表单项样式优化 */
  :deep(.ant-form-item-label) {
    min-width: 70px;
    font-size: 12px;
    font-weight: 500;
    color: #333;
  }

  :deep(.ant-form-item-control) {
    flex: 1;
    min-width: 0;
  }

  :deep(.ant-input) {
    height: 24px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  :deep(.ant-input:focus) {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgb(24 144 255 / 10%);
  }

  :deep(.ant-select) {
    font-size: 12px;
  }

  :deep(.ant-select .ant-select-selector) {
    height: 24px !important;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  :deep(.ant-select-single .ant-select-selector) {
    height: 24px !important;
    line-height: 22px !important;
  }

  :deep(.ant-select-single .ant-select-selector .ant-select-selection-item) {
    line-height: 22px !important;
  }

  :deep(.ant-select-single .ant-select-selector .ant-select-selection-placeholder) {
    line-height: 22px !important;
  }

  :deep(.ant-select:not(.ant-select-disabled):hover .ant-select-selector) {
    border-color: #1890ff;
  }

  :deep(.ant-select-focused .ant-select-selector) {
    border-color: #1890ff !important;
    box-shadow: 0 0 0 2px rgb(24 144 255 / 10%) !important;
  }

  :deep(.ant-picker) {
    height: 24px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  :deep(.ant-picker:hover) {
    border-color: #1890ff;
  }

  :deep(.ant-picker-focused) {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgb(24 144 255 / 10%);
  }

  /* 月份选择器特殊样式 */
  :deep(.ant-picker-month) {
    font-size: 13px;
  }

  :deep(.ant-picker-month .ant-picker-input > input) {
    font-size: 13px;
  }

  /* 日期范围选择器样式优化 */
  :deep(.ant-picker-range) {
    width: 100% !important;
    min-width: 0 !important; /* 允许自适应，但会被媒体查询覆盖 */
    max-width: 100% !important; /* 确保不超出容器 */
    height: 24px; /* 设置合适的高度 */
  }

  :deep(.ant-picker-range .ant-picker-input) {
    flex: 1; /* 确保输入框平均分配空间 */
  }

  :deep(.ant-picker-range .ant-picker-input > input) {
    padding: 2px 4px !important; /* 减少内边距 */

    /* 当日期文本过长时的处理 */
    overflow: hidden;
    font-size: 12px !important; /* 减小字体 */
    font-weight: 400;
    line-height: 1.2; /* 优化行高 */
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  :deep(.ant-picker-range .ant-picker-range-separator) {
    flex-shrink: 0; /* 防止分隔符被压缩 */
    padding: 0 4px; /* 减少分隔符间距 */
    font-size: 12px;
    color: #999;
  }

  /* 确保日期选择器下拉面板不被遮挡 */
  :deep(.ant-picker-dropdown) {
    z-index: 9999;
  }

  /* 日期选择器悬停和焦点状态优化 */
  :deep(.ant-picker-range:hover) {
    border-color: #1890ff;
  }

  :deep(.ant-picker-range.ant-picker-focused) {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgb(24 144 255 / 10%);
  }


</style>
