// 页面布局相关样式

/* 页面容器布局 */
.voucher-page-container {
  box-sizing: border-box;
  height: calc(100vh - 88px);
  padding: 8px 12px;
  overflow: auto;
}

/* 主卡片样式 */
.voucher-main-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 6%);

  :deep(.ant-card-body) {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 0;
    overflow: hidden;
  }
}

/* 头部区域样式 */
.voucher-header {
  flex-shrink: 0;
  padding: 4px 16px 0;
  background: #fff;
}

/* 筛选区域样式 */
.voucher-filter-section {
  flex-shrink: 0;
  padding: 0 20px 12px;
  background: #fff;
}

/* 表格容器样式 */
.voucher-table-container {
  flex: 1;
  padding: 0 16px;
  overflow: hidden;
  background: #fff;
}

/* 标签页样式 */
.voucher-tabs {
  margin-bottom: 0;
  font-size: 14px;

  :deep(.ant-tabs-tab) {
    padding: 8px 16px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;

    &:hover {
      color: #1890ff;
    }

    &.ant-tabs-tab-active {
      font-weight: 600;
      color: #1890ff !important;
    }
  }

  :deep(.ant-tabs-ink-bar) {
    height: 2px;
    background: #1890ff;
    border-radius: 1px;
  }
}
