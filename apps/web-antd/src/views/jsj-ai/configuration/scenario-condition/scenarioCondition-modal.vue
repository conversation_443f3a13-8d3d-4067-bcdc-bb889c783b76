<script setup lang="ts">
  import { computed, onMounted, ref, watch, watchEffect } from 'vue';

  import { useVbenModal } from '@vben/common-ui';
  import { $t } from '@vben/locales';
  import { cloneDeep } from '@vben/utils';

  import { message } from 'ant-design-vue';

  import { useVbenForm } from '#/adapter/form';
  import {
    addScenarioCondition,
    fetchScenarioConditionConfig,
    updateScenarioCondition,
  } from '#/api/jsj-ai/api-v2';
  import { useCompanySelection } from '#/hooks/jsj-ai/ai-chat/useCompanySelection';
  import { defaultFormValueGetter, useBeforeCloseDiff } from '#/utils/popup';

  import { modalSchema } from './data';

  const emit = defineEmits<{ reload: [] }>();

  // 使用公司选择功能
  const { companyList, fetchCompanyNames, selectedCompany } =
    useCompanySelection();

  const isUpdate = ref(false);

  // 场景条件配置数据
  const scenarioConfigData = ref<any[]>([]);
  const selectedTypeConfig = ref<any>(null);
  const currentCompanyName = ref('');



  const [BasicForm, formApi] = useVbenForm({
    commonConfig: {
      // 通用配置项 会影响到所有表单项
      componentProps: {
        class: 'w-full',
      },
      // 默认占满一列
      formItemClass: 'col-span-1',
      // 默认label宽度 px
      labelWidth: 100,
    },
    schema: modalSchema(),
    showDefaultActions: false,
    wrapperClass: 'grid-cols-1',
  });



  // 监听公司名称变化
  watchEffect(async () => {
    const newValue = selectedCompany.value;
    if (newValue !== undefined) {
      currentCompanyName.value = newValue || '';
      if (formApi?.form) {
        await formApi.form.setFieldValue('company_name', newValue || '');
      }
    }
  });

  // 监听表单值变化
  watchEffect(() => {
    const companyName = formApi?.form?.values?.company_name;
    if (companyName !== undefined) {
      currentCompanyName.value = companyName || '';
    }
  });

  const title = computed(() => {
    return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
  });

  const { markInitialized, onBeforeClose, resetInitialized } =
    useBeforeCloseDiff({
      currentGetter: defaultFormValueGetter(formApi),
      initializedGetter: defaultFormValueGetter(formApi),
    });

  const [BasicModal, modalApi] = useVbenModal({
    // 调整模态框宽度为更合适的尺寸
    class: 'w-[600px]',
    fullscreenButton: false,
    onBeforeClose,
    onClosed: handleClosed,
    onConfirm: handleConfirm,
    onOpenChange: async (isOpen) => {
      if (!isOpen) {
        return null;
      }
      modalApi.modalLoading(true);

      // 确保公司数据和场景配置已加载
      await fetchCompanyNames();
      await fetchScenarioConfig();
      updateModalCompanyOptions();

      const { record } = modalApi.getData() as { record?: any };
      isUpdate.value = !!record;

      if (isUpdate.value && record) {
        // 直接设置表单数据，条件数据保持数组格式
        const formData = { ...record };
        await formApi.setValues(formData);

        // 设置选中的类型配置
        if (record.type) {
          selectedTypeConfig.value = scenarioConfigData.value.find(
            (item) => item.config_key === record.type,
          );
          // 更新条件输入选项
          updateConditionInputOptions();
        }
      }
      await markInitialized();

      modalApi.modalLoading(false);
    },
  });

  // 获取场景条件配置
  async function fetchScenarioConfig() {
    try {
      const result = await fetchScenarioConditionConfig({
        config_type: 'scene_condition',
      });
      scenarioConfigData.value = result;
      updateTypeOptions();
      console.log('✅ 场景条件配置已加载:', result);
    } catch (error) {
      console.error('❌ 获取场景条件配置异常:', error);
      message.error('获取场景条件配置异常');
    }
  }

  // 更新类型选项
  function updateTypeOptions() {
    const typeOptions = scenarioConfigData.value.map((item) => ({
      label: item.config_key,
      value: item.config_key,
    }));

    if (formApi && formApi.updateSchema) {
      formApi.updateSchema([
        {
          componentProps: {
            onChange: handleTypeChange,
            options: typeOptions,
          },
          fieldName: 'type',
        },
      ]);
      console.log('✅ 类型选项已更新:', typeOptions);
    }
  }

  // 处理类型变化
  function handleTypeChange(value: string) {
    console.log('🎯 类型变化:', value);
    selectedTypeConfig.value = scenarioConfigData.value.find(
      (item) => item.config_key === value,
    );

    // 清空条件配置的值
    if (formApi && formApi.setFieldValue) {
      formApi.setFieldValue('condition', []);
      console.log('🧹 已清空条件配置值');
    }

    if (selectedTypeConfig.value) {
      console.log('✅ 选中类型配置:', selectedTypeConfig.value);
      // 更新条件输入组件的选项
      updateConditionInputOptions();
    }
  }

  // 更新条件输入组件的选项
  function updateConditionInputOptions() {
    if (formApi && formApi.updateSchema && selectedTypeConfig.value) {
      formApi.updateSchema([
        {
          componentProps: {
            conditionOptions: selectedTypeConfig.value.details || [],
          },
          fieldName: 'condition',
        },
      ]);
      console.log('✅ 条件输入选项已更新:', selectedTypeConfig.value.details);
    }
  }

  // 初始化公司数据和场景配置
  onMounted(async () => {
    await fetchCompanyNames();
    await fetchScenarioConfig();
    updateModalCompanyOptions();
  });

  // 监听公司列表变化，更新表单选项
  watch(
    companyList,
    () => {
      updateModalCompanyOptions();
    },
    { deep: true },
  );

  // 处理模态框中的公司选择变化
  function handleModalCompanyChange(value: string) {
    console.log('🔄 模态框公司选择变化:', value);
    // 更新全局选中的公司
    selectedCompany.value = value;
  }

  // 更新模态框表单中的公司选项
  function updateModalCompanyOptions() {
    const companyOptions = companyList.value.map((company) => ({
      label: company.name,
      value: company.name,
    }));

    console.log('🏢 模态框更新公司选项:', {
      公司列表: companyOptions.map((c) => c.label),
      公司数量: companyOptions.length,
    });

    // 使用 formApi 的 updateSchema 方法来更新字段配置
    if (formApi && formApi.updateSchema) {
      formApi.updateSchema([
        {
          componentProps: {
            onChange: handleModalCompanyChange, // 添加变化处理
            options: companyOptions,
          },
          fieldName: 'company_name',
        },
      ]);
      console.log('✅ 模态框公司选项已更新');

      // 设置默认选中的公司
      const currentSelected = selectedCompany.value;
      if (currentSelected && formApi.setFieldValue) {
        formApi.setFieldValue('company_name', currentSelected);
        console.log('🎯 模态框设置默认选中公司:', currentSelected);
      }
    } else {
      console.warn('⚠️ 模态框 formApi 不可用');
    }
  }

  async function handleConfirm() {
    try {
      modalApi.lock(true);
      const { valid } = await formApi.validate();
      if (!valid) {
        return;
      }

      // getValues获取为一个readonly的对象 需要修改必须先深拷贝一次
      const data = cloneDeep(await formApi.getValues());

      // 处理条件数据，确保是数组格式
      if (data.condition && Array.isArray(data.condition)) {
        // 验证条件数据格式
        for (const item of data.condition) {
          if (!item.name || !item.value) {
            message.error('每个条件必须包含名称和值');
            return;
          }
        }
      } else {
        data.condition = [];
      }

      if (isUpdate.value) {
        await updateScenarioCondition(data as any);
        message.success('更新成功');
      } else {
        await addScenarioCondition(data as any);
        message.success('添加成功');
      }

      resetInitialized();
      emit('reload');
      modalApi.close();
    } catch (error) {
      console.error(error);
      message.error('操作失败，请重试');
    } finally {
      modalApi.lock(false);
    }
  }

  async function handleClosed() {
    await formApi.resetForm();
    resetInitialized();
  }
</script>

<template>
  <BasicModal :title="title">
    <BasicForm />
  </BasicModal>
</template>
