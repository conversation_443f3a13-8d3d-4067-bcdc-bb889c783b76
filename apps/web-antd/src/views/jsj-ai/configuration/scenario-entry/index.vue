<script setup lang="ts">
  import type { TableColumnsType } from 'ant-design-vue';

  import { computed, h, onMounted, reactive, ref, watch } from 'vue';

  import { useVbenModal } from '@vben/common-ui';

  import { message } from 'ant-design-vue';

  import {
    deleteScenarioEntry,
    fetchScenarioEntryList,
  } from '#/api/jsj-ai/api-v2';
  import { useCompanySelection } from '#/hooks/jsj-ai/ai-chat/useCompanySelection';

  import scenarioEntryModal from './scenarioEntry-modal.vue';

  // 使用公司选择功能
  const { selectedCompany } = useCompanySelection();

  // 表单数据
  const searchForm = reactive({
    scene: '',
    type: '',
  });

  // 表格数据
  const allData = ref<any[]>([]); // 存储所有原始数据
  const dataSource = ref<any[]>([]); // 显示的过滤后数据
  const loading = ref(false);
  const sceneOptions = ref<{ label: string; value: string }[]>([]); // 场景选项



  // 从已获取的数据中提取场景选项
  function updateSceneOptions() {
    if (!allData.value || allData.value.length === 0) {
      sceneOptions.value = [];
      return;
    }

    // 提取唯一的场景名称
    const uniqueScenes = [
      ...new Set(allData.value.map((item) => item.scene)),
    ].filter(Boolean);

    sceneOptions.value = uniqueScenes.map((scene) => ({
      label: scene,
      value: scene,
    }));

    console.log('更新场景选项:', sceneOptions.value.length, '个');
  }

  // 前端搜索过滤函数
  function filterData() {
    let filtered = [...allData.value];

    // 按场景过滤
    if (searchForm.scene.trim()) {
      filtered = filtered.filter((item) =>
        item.scene?.toLowerCase().includes(searchForm.scene.toLowerCase()),
      );
    }

    // 按类型过滤
    if (searchForm.type.trim()) {
      filtered = filtered.filter((item) =>
        item.type?.toLowerCase().includes(searchForm.type.toLowerCase()),
      );
    }

    dataSource.value = filtered;
    console.log('前端过滤结果:', filtered.length, '条');
  }

  // 表格列定义
  const columns: TableColumnsType = [
    {
      dataIndex: 'company_name',
      ellipsis: true,
      fixed: false,
      key: 'company_name',
      minWidth: 200,
      title: '公司名称',
      width: 200,
    },
    {
      dataIndex: 'scene',
      ellipsis: true,
      fixed: false,
      key: 'scene',
      minWidth: 180,
      title: '场景',
      width: 180,
    },
    {
      dataIndex: 'type',
      ellipsis: true,
      key: 'type',
      minWidth: 120,
      responsive: ['md'],
      title: '类型',
      width: 120,
    },
    {
      customRender: ({ record }) => {
        const isEnabled = record.status === 1 || record.status === '1';
        return h(
          'span',
          {
            class: `status-badge ${isEnabled ? 'status-enabled' : 'status-disabled'}`,
          },
          isEnabled ? '启用' : '禁用',
        );
      },
      dataIndex: 'status',
      ellipsis: true,
      key: 'status',
      responsive: ['sm'],
      title: '状态',
      width: 80,
    },
    {
      customRender: ({ record }) => {
        if (!record.detail || !Array.isArray(record.detail)) {
          return h('span', { class: 'text-gray-400' }, '无分录');
        }

        const details = record.detail;
        const isSingleEntry = details.length === 1;

        if (isSingleEntry) {
          // 单条分录，横向展示
          const item = details[0];
          return h('div', { class: 'entry-detail-single' }, [
            h('div', { class: 'entry-item' }, [
              h('span', { class: 'entry-label' }, '科目：'),
              h('span', { class: 'entry-value' }, item.subject || '-'),
            ]),
            h('div', { class: 'entry-item' }, [
              h('span', { class: 'entry-label' }, '方向：'),
              h(
                'span',
                {
                  class: `entry-value direction-badge ${item.direction === '借' ? 'direction-debit' : 'direction-credit'}`,
                },
                item.direction || '-',
              ),
            ]),
            h('div', { class: 'entry-item' }, [
              h('span', { class: 'entry-label' }, '数据源：'),
              h('span', { class: 'entry-value' }, item.source || '-'),
            ]),
            h('div', { class: 'entry-item' }, [
              h('span', { class: 'entry-label' }, '正负：'),
              h(
                'span',
                {
                  class: `entry-value negative-badge ${item.negative === '1' ? 'negative-negative' : 'negative-positive'}`,
                },
                item.negative === '1' ? '负' : '正',
              ),
            ]),
          ]);
        } else {
          // 多条分录，两列展示
          return h(
            'div',
            { class: 'entry-detail-multiple' },
            details.map((item: any, index: number) =>
              h(
                'div',
                {
                  class: `entry-row ${index % 2 === 0 ? 'entry-row-even' : 'entry-row-odd'}`,
                  key: index,
                },
                [
                  // h('div', { class: 'entry-index' }, `${index + 1}.`),
                  h('div', { class: 'entry-content' }, [
                    h('div', { class: 'entry-field' }, [
                      h('span', { class: 'field-label' }, '科目：'),
                      h('span', { class: 'field-value' }, item.subject || '-'),
                    ]),
                    h('div', { class: 'entry-field' }, [
                      h('span', { class: 'field-label' }, '方向：'),
                      h(
                        'span',
                        {
                          class: `field-value direction-badge ${item.direction === '借' ? 'direction-debit' : 'direction-credit'}`,
                        },
                        item.direction || '-',
                      ),
                    ]),
                    h('div', { class: 'entry-field' }, [
                      h('span', { class: 'field-label' }, '数据源：'),
                      h('span', { class: 'field-value' }, item.source || '-'),
                    ]),
                    h('div', { class: 'entry-field' }, [
                      h('span', { class: 'field-label' }, '正负：'),
                      h(
                        'span',
                        {
                          class: `field-value negative-badge ${item.negative === '1' ? 'negative-negative' : 'negative-positive'}`,
                        },
                        item.negative === '1' ? '负' : '正',
                      ),
                    ]),
                  ]),
                ],
              ),
            ),
          );
        }
      },
      dataIndex: 'detail',
      key: 'detail',
      minWidth: 300,
      responsive: ['lg'],
      title: '分录详情',
      width: 400,
    },
    {
      customRender: ({ text }) => {
        if (!text) return '';
        return new Date(text).toLocaleString('zh-CN');
      },
      dataIndex: 'created_at',
      key: 'created_at',
      responsive: ['xl'],
      title: '创建时间',
      width: 160,
    },
    {
      customRender: ({ text }) => {
        if (!text) return '';
        return new Date(text).toLocaleString('zh-CN');
      },
      dataIndex: 'updated_at',
      key: 'updated_at',
      responsive: ['xxl'],
      title: '更新时间',
      width: 160,
    },
    {
      fixed: 'right',
      key: 'action',
      title: '操作',
      width: 180,
    },
  ];

  const [ScenarioEntryModal, modalApi] = useVbenModal({
    connectedComponent: scenarioEntryModal,
  });

  // 查询数据（只按公司过滤，获取所有数据）
  async function fetchData() {
    if (!selectedCompany.value) {
      message.warning('请选择公司');
      return;
    }

    try {
      loading.value = true;
      const params: any = {
        company_name: selectedCompany.value,
      };

      const result = await fetchScenarioEntryList(params);
      allData.value = result || [];
      console.log('查询场景分录数据:', allData.value.length, '条');

      // 获取数据后显示所有数据，不自动过滤
      dataSource.value = [...allData.value];

      // 更新场景选项
      updateSceneOptions();
    } catch (error) {
      console.error('查询场景分录列表失败:', error);
      message.error('查询失败，请重试');
      allData.value = [];
      dataSource.value = [];
      sceneOptions.value = [];
    } finally {
      loading.value = false;
    }
  }



  // 处理场景选择变化
  function handleSceneChange(value: any) {
    searchForm.scene = String(value || '');
    filterData();
  }

  // 处理搜索
  function handleSearch() {
    filterData();
  }

  // 处理重置
  function handleReset() {
    searchForm.scene = '';
    searchForm.type = '';
    filterData();
  }



  // 处理编辑
  function handleEdit(record: any) {
    modalApi.setData({ record });
    modalApi.open();
  }

  // 处理删除
  async function handleDelete(record: any) {
    try {
      await deleteScenarioEntry({
        id: record._id,
      });
      message.success('删除成功');
      fetchData();
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败，请重试');
    }
  }

  // 初始化数据
  onMounted(async () => {
    // 如果已经有选中的公司，直接获取数据
    if (selectedCompany.value) {
      await fetchData();
    }
  });

  // 监听全局选中公司变化
  watch(
    selectedCompany,
    (newCompany: string) => {
      if (newCompany) {
        searchForm.scene = ''; // 清空场景选择
        searchForm.type = ''; // 清空类型选择
        fetchData(); // 这里会同时更新数据和场景选项
        console.log('全局公司变化，重新获取数据:', newCompany);
      }
    },
    { immediate: true }, // 初始化时会自动触发一次
  );
</script>

<template>
  <div class="p-4">
    <!-- 查询条件和操作按钮 -->
    <a-card class="mb-4">
      <div class="search-container">
        <!-- 查询表单 -->
        <a-form layout="inline" :model="searchForm" class="search-form">

          <a-form-item label="场景" class="form-item-scene">
            <a-select
              v-model:value="searchForm.scene"
              placeholder="请选择场景"
              class="scene-select"
              :options="sceneOptions"
              show-search
              :filter-option="
                (input: string, option: any) =>
                  option?.label?.toLowerCase().includes(input.toLowerCase())
              "
              @change="handleSceneChange"
            />
          </a-form-item>
          <a-form-item label="类型" class="form-item-type">
            <a-input
              v-model:value="searchForm.type"
              placeholder="请输入类型"
              class="type-input"
              @press-enter="handleSearch"
            />
          </a-form-item>
          <a-form-item class="form-item-actions">
            <a-space>
              <a-button type="primary" @click="handleSearch">查询</a-button>
              <a-button @click="handleReset">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>
    </a-card>

    <!-- 数据表格 -->
    <a-card title="记账场景分录配置列表">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :scroll="{ x: 1400 }"
        row-key="_id"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-popconfirm
                title="确认删除？"
                placement="left"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" size="small" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 模态框 -->
    <ScenarioEntryModal @reload="fetchData" />
  </div>
</template>

<style scoped>
  /* 响应式布局：侧边栏收起时每行最多3个字段 */
  @media (max-width: 1400px) {
    .search-form .ant-form-item {
      margin-right: 12px;
    }

    .search-form .form-item-company .company-select {
      min-width: 240px;
      max-width: 280px;
    }

    .search-form .form-item-scene .scene-select {
      min-width: 220px;
      max-width: 280px;
    }

    .search-form .form-item-type .type-input {
      min-width: 140px;
      max-width: 160px;
    }
  }

  /* 小屏幕下的表格优化 */
  @media (max-width: 768px) {
    :deep(.ant-table-tbody > tr > td:nth-child(1)),
    :deep(.ant-table-thead > tr > th:nth-child(1)) {
      min-width: 150px !important;
    }

    :deep(.ant-table-tbody > tr > td:nth-child(2)),
    :deep(.ant-table-thead > tr > th:nth-child(2)) {
      min-width: 130px !important;
    }
  }

  .search-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: flex-start;
    justify-content: space-between;
  }

  .search-form {
    flex: 1;
    min-width: 600px;
  }

  .action-buttons {
    display: flex;
    flex-shrink: 0;
    align-items: center;
  }

  .search-form .ant-form-item {
    margin-bottom: 8px;
  }

  /* 搜索表单字段样式 */
  .search-form .form-item-company .company-select {
    width: 100%;
    min-width: 280px;
    max-width: 350px;
  }

  .search-form .form-item-scene .scene-select {
    width: 100%;
    min-width: 280px;
    max-width: 350px;
  }

  .search-form .form-item-type .type-input {
    width: 100%;
    min-width: 160px;
    max-width: 200px;
  }

  /* 状态徽章样式 */
  :deep(.status-badge) {
    display: inline-block !important;
    min-width: 40px !important;
    padding: 2px 8px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    line-height: 1.4 !important;
    text-align: center !important;
    border-radius: 12px !important;
  }

  :deep(.status-enabled) {
    color: #52c41a !important;
    background-color: #f6ffed !important;
    border: 1px solid #b7eb8f !important;
  }

  :deep(.status-disabled) {
    color: #ff4d4f !important;
    background-color: #fff2f0 !important;
    border: 1px solid #ffccc7 !important;
  }

  /* 分录详情样式 */
  :deep(.entry-detail-single) {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    padding: 8px;
    background-color: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
  }

  :deep(.entry-item) {
    display: flex;
    gap: 4px;
    align-items: center;
  }

  :deep(.entry-label) {
    font-size: 12px;
    font-weight: 500;
    color: #666;
  }

  :deep(.entry-value) {
    font-size: 12px;
    color: #333;
  }

  /* 多条分录样式 */
  :deep(.entry-detail-multiple) {
    max-height: 200px;
    overflow-y: auto;
  }

  :deep(.entry-row) {
    display: flex;
    gap: 8px;
    padding: 6px 8px;
    margin-bottom: 4px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
  }

  :deep(.entry-row-even) {
    background-color: #fafafa;
  }

  :deep(.entry-row-odd) {
    background-color: #fff;
  }

  :deep(.entry-index) {
    min-width: 20px;
    padding-top: 2px;
    font-size: 12px;
    font-weight: 500;
    color: #999;
  }

  :deep(.entry-content) {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    gap: 8px;
  }

  :deep(.entry-field) {
    display: flex;
    gap: 2px;
    align-items: center;
    min-width: 80px;
  }

  :deep(.field-label) {
    font-size: 11px;
    font-weight: 500;
    color: #666;
  }

  :deep(.field-value) {
    font-size: 11px;
    color: #333;
  }

  /* 方向徽章样式 */
  :deep(.direction-badge) {
    display: inline-block !important;
    min-width: 24px !important;
    padding: 1px 6px !important;
    font-size: 10px !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
    text-align: center !important;
    border-radius: 8px !important;
  }

  :deep(.direction-debit) {
    color: #1890ff !important;
    background-color: #e6f7ff !important;
    border: 1px solid #91d5ff !important;
  }

  :deep(.direction-credit) {
    color: #eb2f96 !important;
    background-color: #fff0f6 !important;
    border: 1px solid #ffadd2 !important;
  }

  /* 正负徽章样式 */
  :deep(.negative-badge) {
    display: inline-block !important;
    min-width: 24px !important;
    padding: 1px 6px !important;
    font-size: 10px !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
    text-align: center !important;
    border-radius: 8px !important;
  }

  :deep(.negative-positive) {
    color: #52c41a !important;
    background-color: #f6ffed !important;
    border: 1px solid #b7eb8f !important;
  }

  :deep(.negative-negative) {
    color: #ff4d4f !important;
    background-color: #fff2f0 !important;
    border: 1px solid #ffccc7 !important;
  }

  /* 表格重要列样式 - 确保公司名称和场景列不被过度压缩 */
  :deep(.ant-table-tbody > tr > td:nth-child(1)),
  :deep(.ant-table-thead > tr > th:nth-child(1)) {
    min-width: 200px !important;
  }

  :deep(.ant-table-tbody > tr > td:nth-child(2)),
  :deep(.ant-table-thead > tr > th:nth-child(2)) {
    min-width: 180px !important;
  }
</style>
