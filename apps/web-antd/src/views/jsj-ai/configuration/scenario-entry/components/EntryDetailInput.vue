<script setup lang="ts">
  import { computed, onMounted, ref, watch } from 'vue';

  import { DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue';

  import { fetchScenarioConditionConfig } from '#/api/jsj-ai/api-v2';
  import { useAccountSubjects } from '#/hooks/jsj-ai/account-book/voucher/index';

  interface EntryDetail {
    direction: '借' | '贷';
    negative?: string; // 正负标识：1 为负数，其他都为正数
    source: string;
    subject: string;
  }

  interface Props {
    selectedType?: string; // 从主表单传递的类型值
    value?: EntryDetail[];
    scenarioConfig?: any[]; // 从父组件传递的场景配置数据
  }

  interface Emits {
    (e: 'update:value', value: EntryDetail[]): void;
    (e: 'change', value: EntryDetail[]): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    selectedType: '',
    value: () => [],
    scenarioConfig: () => [],
  });

  const emit = defineEmits<Emits>();

  // 使用会计科目 hooks
  const {
    error: subjectError,
    loading: subjectLoading,
    subjectOptions,
  } = useAccountSubjects();

  // 将科目选项转换为以字符串为值的格式（用于接口传递）
  const subjectStringOptions = computed(() => {
    const options = subjectOptions.value.map((option) => ({
      code: option.code,
      label: option.label,
      name: option.name,
      value: option.label, // 使用label作为value，这样存储和传递的就是字符串
    }));
    console.log('🏷️ EntryDetailInput: 科目字符串选项数量:', options.length);
    return options;
  });

  // 场景配置数据
  const scenarioConfigData = ref<any[]>([]);
  const configLoading = ref(false);

  // 内部分录列表
  const details = ref<EntryDetail[]>([]);

  // 方向选项
  const directionOptions = [
    { label: '借', value: '借' },
    { label: '贷', value: '贷' },
  ];

  // 来源选项（基于传入的类型）
  const sourceOptions = computed(() => {
    if (!props.selectedType) {
      console.log('⚠️ EntryDetailInput: 没有选中的类型，数据源选项为空');
      return [];
    }

    const typeConfig = scenarioConfigData.value.find(
      (item) => item.config_key === props.selectedType,
    );

    if (typeConfig && typeConfig.details) {
      const options = typeConfig.details.map((detail: any) => ({
        label: detail.name,
        value: detail.name,
      }));
      console.log('✅ EntryDetailInput: 生成数据源选项:', {
        optionsCount: options.length,
        selectedType: props.selectedType,
      });
      return options;
    }
    console.log('❌ EntryDetailInput: 没有找到匹配的类型配置', {
      configDataLength: scenarioConfigData.value.length,
      selectedType: props.selectedType,
    });
    return [];
  });

  // 获取场景配置数据（优先使用props传递的数据）
  const fetchScenarioConfig = async () => {
    // 如果父组件已经传递了配置数据，直接使用
    if (props.scenarioConfig && props.scenarioConfig.length > 0) {
      scenarioConfigData.value = props.scenarioConfig;
      console.log('✅ 使用父组件传递的场景配置数据');
      return;
    }

    // 否则从API获取
    try {
      configLoading.value = true;
      const result = await fetchScenarioConditionConfig({
        config_type: 'scene_condition',
      });
      scenarioConfigData.value = result;
      console.log('✅ 从API获取场景配置数据:', result);
    } catch (error) {
      console.error('❌ 获取场景配置异常:', error);
    } finally {
      configLoading.value = false;
    }
  };

  // 初始化分录列表
  const initDetails = () => {
    if (props.value && props.value.length > 0) {
      // 确保每个分录都有negative字段
      details.value = props.value.map((item) => ({
        ...item,
        negative: item.negative || '0', // 如果没有negative字段，默认为'0'（正数）
      }));
    } else {
      details.value = [
        { direction: '借', negative: '0', source: '', subject: '' },
      ];
    }
  };

  // 监听外部值变化
  watch(
    () => props.value,
    (newValue) => {
      if (newValue && newValue.length > 0) {
        // 确保每个分录都有negative字段
        details.value = newValue.map((item) => ({
          ...item,
          negative: item.negative || '0', // 如果没有negative字段，默认为'0'（正数）
        }));
        console.log(
          '✅ EntryDetailInput: 设置分录详情，条数:',
          details.value.length,
          '数据源值:',
          details.value.map((d) => d.source),
        );
      } else if (details.value.length === 0) {
        details.value = [
          { direction: '借', negative: '0', source: '', subject: '' },
        ];
        console.log('🆕 EntryDetailInput: 设置默认分录详情');
      }
    },
    { immediate: true },
  );

  // 监听类型变化，清空来源值
  watch(
    () => props.selectedType,
    (newType, oldType) => {
      console.log('🔄 EntryDetailInput: 类型变化:', {
        hasDetails: details.value.length > 0,
        newType,
        oldType,
      });
      // 只有在oldType不为空且与newType不同时才处理来源值
      // 这样可以避免在编辑模式下初始化时错误清空数据
      if (oldType && newType !== oldType && details.value.length > 0) {
        // 获取新类型的可用数据源选项
        const newTypeConfig = scenarioConfigData.value.find(
          (item) => item.config_key === newType,
        );
        const newSourceOptions =
          newTypeConfig?.details?.map((detail: any) => detail.name) || [];

        console.log(
          '🔍 EntryDetailInput: 新类型的数据源选项:',
          newSourceOptions,
        );

        // 检查现有的数据源值是否在新类型的选项中，如果不在则清空
        let hasChanges = false;
        details.value.forEach((detail) => {
          if (detail.source && !newSourceOptions.includes(detail.source)) {
            console.log(
              '🗑️ EntryDetailInput: 清空无效的数据源值:',
              detail.source,
            );
            detail.source = '';
            hasChanges = true;
          }
        });

        if (hasChanges) {
          emitUpdate();
          console.log(
            '🎯 EntryDetailInput: 类型变化，已验证并清理无效的来源值',
          );
        } else {
          console.log('✅ EntryDetailInput: 类型变化，所有数据源值仍然有效');
        }
      } else {
        console.log('ℹ️ EntryDetailInput: 跳过类型变化处理 - 初始化或无变化');
      }
    },
    { flush: 'post' }, // 确保在DOM更新后执行
  );

  // 科目选择器过滤函数
  const filterSubjectOption = (input: string, option: any) => {
    const searchText = input.toLowerCase();
    return (
      option.label?.toLowerCase().includes(searchText) ||
      option.code?.toLowerCase().includes(searchText) ||
      option.name?.toLowerCase().includes(searchText)
    );
  };

  // 发送更新事件的函数
  const emitUpdate = () => {
    emit('update:value', [...details.value]);
    emit('change', [...details.value]);
  };

  // 添加分录
  const addDetail = () => {
    details.value.push({
      direction: '借',
      negative: '0',
      source: '',
      subject: '',
    });
    emitUpdate();
  };

  // 删除分录
  const removeDetail = (index: number) => {
    if (details.value.length > 1) {
      details.value.splice(index, 1);
      emitUpdate();
    }
  };

  // 初始化
  initDetails();

  // 监听父组件传递的配置数据变化
  watch(
    () => props.scenarioConfig,
    (newConfig) => {
      if (newConfig && newConfig.length > 0) {
        scenarioConfigData.value = newConfig;
        console.log('✅ 更新场景配置数据:', newConfig);
      }
    },
    { immediate: true },
  );

  // 组件挂载时获取场景配置数据（如果父组件没有传递）
  onMounted(() => {
    fetchScenarioConfig();
  });
</script>

<template>
  <div class="entry-detail-input">
    <div v-for="(detail, index) in details" :key="index" class="detail-item">
      <div class="detail-row">
        <div class="detail-field">
          <a-select
            v-model:value="detail.subject"
            placeholder="请选择科目"
            class="detail-subject"
            show-search
            allow-clear
            :loading="subjectLoading"
            :options="subjectStringOptions"
            :filter-option="filterSubjectOption"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            @change="emitUpdate"
          />
        </div>
        <div class="detail-field direction-field">
          <a-select
            v-model:value="detail.direction"
            placeholder="选择方向"
            class="detail-direction"
            :options="directionOptions"
            @change="emitUpdate"
          />
        </div>
        <div class="detail-field">
          <a-select
            v-model:value="detail.source"
            placeholder="请选择来源"
            class="detail-source"
            allow-clear
            :options="sourceOptions"
            @change="emitUpdate"
          />
        </div>
        <div class="detail-field negative-field">
          <a-switch
            v-model:checked="detail.negative"
            checked-value="1"
            un-checked-value="0"
            checked-children="负"
            un-checked-children="正"
            size="small"
            @change="emitUpdate"
          />
        </div>
        <div class="detail-actions">
          <a-button
            v-if="details.length > 1"
            type="text"
            danger
            size="small"
            @click="removeDetail(index)"
          >
            <template #icon>
              <DeleteOutlined />
            </template>
          </a-button>
        </div>
      </div>
    </div>

    <div class="add-detail">
      <a-button type="dashed" block @click="addDetail">
        <template #icon>
          <PlusOutlined />
        </template>
        添加分录
      </a-button>
    </div>
  </div>
</template>

<style scoped>
  .entry-detail-input {
    width: 100%;
  }

  .detail-item {
    margin-bottom: 8px;
  }

  .detail-row {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .detail-field {
    flex: 1;
  }

  .direction-field {
    flex: 0 0 100px;
  }

  .negative-field {
    display: flex;
    flex: 0 0 80px;
    align-items: center;
    justify-content: center;
  }

  .detail-subject {
    width: 100%;
  }

  .detail-direction {
    width: 100%;
  }

  .detail-source {
    width: 100%;
  }

  .detail-actions {
    display: flex;
    flex-shrink: 0;
    justify-content: center;
    width: 32px;
  }

  .add-detail {
    margin-top: 8px;
  }

  /* 选择框样式优化 - 悬浮提示 */
  :deep(.ant-select-dropdown) {
    .ant-select-item-option-content {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
</style>
