<template>
  <div class="h-full p-6">
    <div class="rounded-lg bg-white p-6 shadow-sm">
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">AI工作台概览</h1>
        <p class="mt-2 text-gray-600">
          当前客户：<span class="font-medium text-blue-600">{{ selectedCompany }}</span>
        </p>
        <p class="text-gray-600">
          当前月份：<span class="font-medium text-blue-600">{{ selectedMonth }}</span>
        </p>
      </div>

      <!-- 功能卡片 -->
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <!-- 原始凭证 -->
        <div class="rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer" @click="navigateTo('/voucher/original')">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <FileImageOutlined class="h-8 w-8 text-blue-500" />
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">原始凭证</h3>
              <p class="text-sm text-gray-500">查看和管理原始凭证文件</p>
            </div>
          </div>
        </div>

        <!-- AI记账 -->
        <div class="rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer" @click="navigateTo('/bookkeeping/view')">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <RobotOutlined class="h-8 w-8 text-green-500" />
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">AI记账</h3>
              <p class="text-sm text-gray-500">智能生成记账凭证</p>
            </div>
          </div>
        </div>

        <!-- AI客服 -->
        <div class="rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer" @click="navigateTo('/ai-service/center')">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <CustomerServiceOutlined class="h-8 w-8 text-purple-500" />
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">AI客服</h3>
              <p class="text-sm text-gray-500">智能客服和待办中心</p>
            </div>
          </div>
        </div>

        <!-- 记账助手 -->
        <div class="rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer" @click="navigateTo('/assistant/tools')">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <ToolOutlined class="h-8 w-8 text-orange-500" />
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">记账助手</h3>
              <p class="text-sm text-gray-500">智能记账工具和辅助功能</p>
            </div>
          </div>
        </div>

        <!-- 配置 -->
        <div class="rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer" @click="navigateTo('/configuration/scenario-condition')">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <SettingOutlined class="h-8 w-8 text-red-500" />
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">配置</h3>
              <p class="text-sm text-gray-500">记账场景和分录配置</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="mt-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">最近活动</h2>
        <div class="space-y-3">
          <div class="flex items-center justify-between rounded-lg border border-gray-200 p-4">
            <div class="flex items-center space-x-3">
              <CheckCircleOutlined class="h-5 w-5 text-green-500" />
              <div>
                <h4 class="text-sm font-medium text-gray-900">AI凭证生成完成</h4>
                <p class="text-xs text-gray-500">2024-01-15 14:30</p>
              </div>
            </div>
            <Button size="small">查看</Button>
          </div>
          
          <div class="flex items-center justify-between rounded-lg border border-gray-200 p-4">
            <div class="flex items-center space-x-3">
              <ClockCircleOutlined class="h-5 w-5 text-yellow-500" />
              <div>
                <h4 class="text-sm font-medium text-gray-900">银行回单处理中</h4>
                <p class="text-xs text-gray-500">2024-01-15 13:15</p>
              </div>
            </div>
            <Button size="small">查看</Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { Button } from 'ant-design-vue';
  import {
    CheckCircleOutlined,
    ClockCircleOutlined,
    CustomerServiceOutlined,
    FileImageOutlined,
    RobotOutlined,
    SettingOutlined,
    ToolOutlined,
  } from '@ant-design/icons-vue';
  
  import { useCompanySelectionStore } from '#/store/modules/company-selection';
  import { useMonthSelectionStore } from '#/store/modules/month-selection';

  const router = useRouter();
  const companySelectionStore = useCompanySelectionStore();
  const monthSelectionStore = useMonthSelectionStore();

  // 计算属性
  const selectedCompany = computed(() => {
    return companySelectionStore.getSelectedCompany() || '未选择客户';
  });

  const selectedMonth = computed(() => {
    return monthSelectionStore.getFormattedMonth();
  });

  // 导航到指定页面
  const navigateTo = (path: string) => {
    router.push(path);
  };
</script>
