<script lang="ts" setup>
  import type { TableColumnsType } from 'ant-design-vue';

  import { computed, h, ref } from 'vue';

  import { DownOutlined, SmileOutlined } from '@ant-design/icons-vue';

  // 官方示例数据
  const officialData = [
    {
      address: 'New York No. 1 Lake Park',
      age: 32,
      key: '1',
      name: '<PERSON>',
      tags: ['nice', 'developer'],
    },
    {
      address: 'London No. 1 Lake Park',
      age: 42,
      key: '2',
      name: '<PERSON>',
      tags: ['loser'],
    },
    {
      address: 'Sidney No. 1 Lake Park',
      age: 32,
      key: '3',
      name: '<PERSON>',
      tags: ['cool', 'teacher'],
    },
  ];

  // 简化测试数据
  const simpleData = [
    { age: 25, city: '北京', key: '1', name: '张三' },
    { age: 30, city: '上海', key: '2', name: '李四' },
    { age: 28, city: '广州', key: '3', name: '王五' },
  ];

  // 官方示例列配置
  const officialColumns = ref<TableColumnsType>([
    {
      dataIndex: 'name',
      key: 'name',
      resizable: true,
      width: 150,
    },
    {
      dataIndex: 'age',
      key: 'age',
      maxWidth: 200,
      minWidth: 100,
      resizable: true,
      title: 'Age',
      width: 100,
    },
    {
      dataIndex: 'address',
      key: 'address',
      resizable: true,
      title: 'Address',
      width: 200,
    },
    {
      dataIndex: 'tags',
      key: 'tags',
      resizable: true,
      title: 'Tags',
      width: 150,
    },
    {
      key: 'action',
      title: 'Action',
      width: 200,
    },
  ]);

  // 简化列配置
  const simpleColumns = ref<TableColumnsType>([
    {
      dataIndex: 'name',
      key: 'name',
      resizable: true,
      title: '姓名',
      width: 120,
    },
    {
      dataIndex: 'age',
      key: 'age',
      resizable: true,
      title: '年龄',
      width: 80,
    },
    {
      dataIndex: 'city',
      key: 'city',
      resizable: true,
      title: '城市',
      width: 100,
    },
  ]);

  // 使用 computed 的列配置（模拟 original 页面）
  const computedColumns = computed(() => {
    return [
      {
        dataIndex: 'name',
        key: 'name',
        resizable: true,
        title: '姓名',
        width: 120,
      },
      {
        dataIndex: 'age',
        key: 'age',
        resizable: true,
        title: '年龄',
        width: 80,
      },
      {
        dataIndex: 'city',
        key: 'city',
        resizable: true,
        title: '城市',
        width: 100,
      },
    ];
  });

  // 使用 computed + customRender 的列配置（更接近 original 页面）
  const computedWithRenderColumns = computed(() => {
    return [
      {
        customRender: ({ text }: any) => {
          return h(
            'span',
            {
              title: text || '',
            },
            text || '-',
          );
        },
        dataIndex: 'name',
        key: 'name',
        resizable: true,
        title: '姓名',
        width: 120,
      },
      {
        customRender: ({ text }: any) => {
          return h(
            'span',
            {
              title: text || '',
            },
            text || '-',
          );
        },
        dataIndex: 'age',
        key: 'age',
        resizable: true,
        title: '年龄',
        width: 80,
      },
      {
        customRender: ({ text }: any) => {
          return h(
            'span',
            {
              title: text || '',
            },
            text || '-',
          );
        },
        dataIndex: 'city',
        key: 'city',
        resizable: true,
        title: '城市',
        width: 100,
      },
    ];
  });

  // 使用 ref + 复杂结构的列配置（测试是否是 computed 的问题）
  const refComplexColumns = ref<TableColumnsType>([
    {
      customRender: ({ text }: any) => {
        return h(
          'span',
          {
            title: text || '',
          },
          text || '-',
        );
      },
      dataIndex: 'name',
      key: 'name',
      maxWidth: 200,
      minWidth: 80,
      resizable: true,
      title: '姓名',
      width: 120,
    },
    {
      customRender: ({ text }: any) => {
        return h(
          'span',
          {
            title: text || '',
          },
          text || '-',
        );
      },
      dataIndex: 'age',
      key: 'age',
      maxWidth: 150,
      minWidth: 60,
      resizable: true,
      title: '年龄',
      width: 80,
    },
    {
      customRender: ({ text }: any) => {
        return h(
          'span',
          {
            title: text || '',
          },
          text || '-',
        );
      },
      dataIndex: 'city',
      key: 'city',
      maxWidth: 200,
      minWidth: 80,
      resizable: true,
      title: '城市',
      width: 100,
    },
  ]);

  // 调试信息
  const lastResize = ref('暂无调整记录');

  // 官方示例的拖拽处理
  function handleOfficialResize(w: number, col: any) {
    console.log('官方示例拖拽:', w, col);
    col.width = w;
    lastResize.value = `官方示例: ${col.key || col.dataIndex} -> ${w}px (${new Date().toLocaleTimeString()})`;
  }

  // 简化版本的拖拽处理
  function handleSimpleResize(w: number, col: any) {
    console.log('简化版本拖拽:', w, col);
    col.width = w;
    lastResize.value = `简化版本: ${col.key || col.dataIndex} -> ${w}px (${new Date().toLocaleTimeString()})`;
  }

  // computed 版本的拖拽处理
  function handleComputedResize(w: number, col: any) {
    console.log('computed版本拖拽:', w, col);
    col.width = w;
    lastResize.value = `computed版本: ${col.key || col.dataIndex} -> ${w}px (${new Date().toLocaleTimeString()})`;
  }

  // computed + customRender 版本的拖拽处理
  function handleComputedWithRenderResize(w: number, col: any) {
    console.log('computed+render版本拖拽:', w, col);
    col.width = w;
    lastResize.value = `computed+render版本: ${col.key || col.dataIndex} -> ${w}px (${new Date().toLocaleTimeString()})`;
  }

  // ref + 复杂结构版本的拖拽处理
  function handleRefComplexResize(w: number, col: any) {
    console.log('ref+复杂结构版本拖拽:', w, col);
    col.width = w;
    lastResize.value = `ref+复杂结构版本: ${col.key || col.dataIndex} -> ${w}px (${new Date().toLocaleTimeString()})`;
  }
</script>

<template>
  <div class="test-resize-page">
    <h2>列拖拽测试页面</h2>

    <div class="test-section">
      <h3>官方示例（参考）</h3>
      <a-table
        :columns="officialColumns"
        :data-source="officialData"
        @resize-column="handleOfficialResize"
        bordered
        size="small"
      >
        <template #headerCell="{ column }">
          <template v-if="column.key === 'name'">
            <span>
              <SmileOutlined />
              Name
            </span>
          </template>
        </template>

        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <a>{{ record.name }}</a>
          </template>
          <template v-else-if="column.key === 'tags'">
            <span>
              <a-tag
                v-for="tag in record.tags"
                :key="tag"
                :color="
                  tag === 'loser'
                    ? 'volcano'
                    : tag.length > 5
                      ? 'geekblue'
                      : 'green'
                "
              >
                {{ tag.toUpperCase() }}
              </a-tag>
            </span>
          </template>
          <template v-else-if="column.key === 'action'">
            <span>
              <a>Invite 一 {{ record.name }}</a>
              <a-divider type="vertical" />
              <a>Delete</a>
              <a-divider type="vertical" />
              <a class="ant-dropdown-link">
                More actions
                <DownOutlined />
              </a>
            </span>
          </template>
        </template>
      </a-table>
    </div>

    <div class="test-section">
      <h3>简化测试版本</h3>
      <a-table
        :columns="simpleColumns"
        :data-source="simpleData"
        @resize-column="handleSimpleResize"
        bordered
        size="small"
      />
    </div>

    <div class="test-section">
      <h3>Computed 列配置版本</h3>
      <a-table
        :columns="computedColumns"
        :data-source="simpleData"
        @resize-column="handleComputedResize"
        bordered
        size="small"
      />
    </div>

    <div class="test-section">
      <h3>Computed + CustomRender 版本</h3>
      <a-table
        :columns="computedWithRenderColumns"
        :data-source="simpleData"
        @resize-column="handleComputedWithRenderResize"
        bordered
        size="small"
      />
    </div>

    <div class="test-section">
      <h3>Ref + 复杂结构版本（测试是否是computed的问题）</h3>
      <a-table
        :columns="refComplexColumns"
        :data-source="simpleData"
        @resize-column="handleRefComplexResize"
        bordered
        size="small"
      />
    </div>

    <div class="test-section">
      <h3>调试信息</h3>
      <div class="debug-info">
        <p><strong>官方列配置:</strong></p>
        <pre>{{ JSON.stringify(officialColumns, null, 2) }}</pre>

        <p><strong>简化列配置:</strong></p>
        <pre>{{ JSON.stringify(simpleColumns, null, 2) }}</pre>

        <p><strong>最后调整记录:</strong></p>
        <pre>{{ lastResize }}</pre>
      </div>
    </div>
  </div>
</template>

<style scoped>
  .test-resize-page {
    max-width: 1200px;
    padding: 20px;
    margin: 0 auto;
  }

  .test-section {
    padding: 20px;
    margin-bottom: 40px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
  }

  .test-section h3 {
    margin-top: 0;
    color: #1890ff;
  }

  .debug-info {
    padding: 15px;
    font-size: 12px;
    background: #f5f5f5;
    border-radius: 4px;
  }

  .debug-info pre {
    max-height: 200px;
    padding: 10px;
    overflow-x: auto;
    background: white;
    border-radius: 4px;
  }

  .debug-info p {
    margin: 10px 0 5px;
    font-weight: bold;
  }
</style>
