import { describe, expect, it, vi } from 'vitest';

import { buildFileUrl, buildInvoicePdfUrl, buildVoucherPdfUrl, FileUrlBuilder } from '../file-url';

// Mock useAppConfig
vi.mock('@vben/hooks', () => ({
  useAppConfig: vi.fn(() => ({
    fileServerURL: 'http://test-server.com/files',
  })),
}));

describe('FileUrlBuilder', () => {
  describe('buildFileUrl', () => {
    it('should build basic file URL', () => {
      const result = buildFileUrl('test.pdf', '测试公司');
      expect(result).toBe('http://test-server.com/files/%E6%B5%8B%E8%AF%95%E5%85%AC%E5%8F%B8/test.pdf');
    });

    it('should build bank receipt file URL with month', () => {
      const result = buildFileUrl('receipt.pdf', '测试公司', true, '202312');
      expect(result).toBe('http://test-server.com/files/%E6%B5%8B%E8%AF%95%E5%85%AC%E5%8F%B8/202312/receipt.pdf');
    });

    it('should return # for empty filename', () => {
      const result = buildFileUrl('', '测试公司');
      expect(result).toBe('#');
    });

    it('should return # for empty company name', () => {
      const result = buildFileUrl('test.pdf', '');
      expect(result).toBe('#');
    });
  });

  describe('buildVoucherPdfUrl', () => {
    it('should build voucher PDF URL', () => {
      const result = buildVoucherPdfUrl('voucher.pdf', '测试公司', '202312');
      expect(result).toBe('http://test-server.com/files/%E6%B5%8B%E8%AF%95%E5%85%AC%E5%8F%B8/202312/voucher/voucher.pdf');
    });

    it('should handle special characters in filename', () => {
      const result = buildVoucherPdfUrl('凭证 (1).pdf', '测试公司', '202312');
      expect(result).toBe('http://test-server.com/files/%E6%B5%8B%E8%AF%95%E5%85%AC%E5%8F%B8/202312/voucher/%E5%87%AD%E8%AF%81%20(1).pdf');
    });

    it('should return # for missing parameters', () => {
      expect(buildVoucherPdfUrl('', '测试公司', '202312')).toBe('#');
      expect(buildVoucherPdfUrl('test.pdf', '', '202312')).toBe('#');
      expect(buildVoucherPdfUrl('test.pdf', '测试公司', '')).toBe('#');
    });
  });

  describe('buildInvoicePdfUrl', () => {
    it('should build invoice PDF URL', () => {
      const result = buildInvoicePdfUrl('12345678', '测试公司', '202312');
      expect(result).toBe('http://test-server.com/files/%E6%B5%8B%E8%AF%95%E5%85%AC%E5%8F%B8/202312/12345678.pdf');
    });

    it('should return # for missing parameters', () => {
      expect(buildInvoicePdfUrl('', '测试公司', '202312')).toBe('#');
      expect(buildInvoicePdfUrl('12345678', '', '202312')).toBe('#');
      expect(buildInvoicePdfUrl('12345678', '测试公司', '')).toBe('#');
    });
  });

  describe('getFileServerURL', () => {
    it('should return file server URL', () => {
      const result = FileUrlBuilder.getFileServerURL();
      expect(result).toBe('http://test-server.com/files');
    });
  });
});
