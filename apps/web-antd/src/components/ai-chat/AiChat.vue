<script setup lang="ts">
  import type { UploadFile } from 'ant-design-vue/es/upload/interface';

  import type { ChatMessage } from '#/store/modules/ai-chat';

  import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';

  import { useUserStore } from '@vben/stores';

  import dayjs from 'dayjs';

  import { useCompanySelection } from '#/hooks/jsj-ai/ai-chat/useCompanySelection';
  import { useFileUpload } from '#/hooks/jsj-ai/ai-chat/useFileUpload';
  import { useMessageHandling } from '#/hooks/jsj-ai/ai-chat/useMessageHandling';
  // 导入composables
  import { useWebSocketConnection } from '#/hooks/jsj-ai/ai-chat/useWebSocketConnection';
  import { useAIChatStore } from '#/store/modules/ai-chat';
  import { safeWebSocketMessageHandler } from '#/utils/websocket-message-filter';

  // 导入子组件
  import ChatHeader from './components/ChatHeader.vue';
  import ChatInput from './components/ChatInput.vue';
  import ChatMessages from './components/ChatMessages.vue';
  import MessageDetailModal from './MessageDetailModal.vue';

  import 'dayjs/locale/zh-cn';

  // 设置dayjs的默认locale
  try {
    dayjs.locale('zh-cn');
  } catch (error) {
    console.warn('Failed to set dayjs locale to zh-cn:', error);
  }

  const WS_URL = 'ws://192.168.20.211:30065';

  // 使用store
  const aiChatStore = useAIChatStore();
  const userStore = useUserStore();
  const router = useRouter();

  // 使用composables
  const {
    connectionStatus,
    reconnectWebSocket,
    sendMessageWithFiles,
    sendUpdateClientInfoMessage,
    setOnConnectedCallback,
    taskProgressMap,
    wsClose,
    wsOpen,
    wsSend,
  } = useWebSocketConnection(WS_URL, (event: MessageEvent) => {
    // 使用安全的消息处理器
    safeWebSocketMessageHandler(
      event.data,
      (data: any) => {
        console.log('处理有效的 WebSocket 消息:', data);
        handleWebSocketMessage(data, scrollToBottom);
      },
      {
        logHeartbeat: false,
        logInvalidMessages: true,
        onError: (error: string, rawData: any) => {
          console.error(`WebSocket 消息处理错误: ${error}`, rawData);
        },
      },
    );
  });

  const {
    clearFiles,
    fileChange,
    getFilesForMessage,
    handleFileUpload,
    isOpen: fileUploadOpen,
    uploadedFiles,
    items: fileItems,
  } = useFileUpload();

  const { selectedCompany, selectedMonth } = useCompanySelection();

  const {
    handleSubmit: handleMessageSubmit,
    handleWebSocketMessage,
    loading,
    messages,
  } = useMessageHandling(taskProgressMap, wsSend);

  // 本地状态
  const inputValue = ref('');
  const detailModalVisible = ref(false);
  const selectedMessage = ref<ChatMessage | null>(null);
  const isInitializing = ref(false);
  const initializationError = ref<null | string>(null);

  // 滚动到底部的函数
  const scrollToBottom = () => {
    console.log('scrollToBottom 被调用');

    // 使用 nextTick 确保 DOM 已更新
    nextTick(() => {
      // 按优先级尝试多个可能的滚动容器
      const containers = [
        '.messages-container', // 主要滚动容器
        '.chat-messages', // ChatMessages 组件的根容器
        '.bubble-list', // Bubble.List 组件
        '.ant-bubble-list', // ant-design-x 的 bubble list
      ];

      for (const selector of containers) {
        const container = document.querySelector(selector);
        if (container) {
          console.log(`检查容器: ${selector}`, {
            clientHeight: container.clientHeight,
            hasOverflow: container.scrollHeight > container.clientHeight,
            scrollHeight: container.scrollHeight,
          });

          // 检查是否需要滚动（内容高度大于容器高度）
          if (container.scrollHeight > container.clientHeight) {
            console.log(`找到可滚动容器: ${selector}`);

            // 立即滚动到底部（不使用平滑滚动，确保立即生效）
            container.scrollTop = container.scrollHeight;

            console.log(`滚动完成，scrollTop: ${container.scrollTop}`);
            return;
          }
        }
      }

      console.warn('未找到可滚动的容器，尝试强制滚动主容器');
      // 如果没有找到可滚动容器，尝试强制滚动主容器
      const mainContainer = document.querySelector('.messages-container');
      if (mainContainer) {
        mainContainer.scrollTop = mainContainer.scrollHeight;
        console.log('强制滚动主容器完成');
      }
    });
  };

  // 处理消息提交
  const handleSubmit = async (content: string) => {
    await handleMessageSubmit(
      content,
      uploadedFiles.value,
      (messageContent: string) => {
        const files = getFilesForMessage();
        sendMessageWithFiles(
          messageContent,
          files,
          selectedCompany.value,
          selectedMonth.value,
        );
        // 清空输入框和文件
        inputValue.value = '';
        clearFiles();
      },
      scrollToBottom,
    );
  };

  // 处理文件变化
  const handleFileChange = (data: { fileList: UploadFile[] }) => {
    fileChange(data, selectedCompany.value);
  };

  // 处理文件上传
  const handleFileUploadWrapper = (firstFile: File, fileList: FileList) => {
    // 这里需要获取attachmentsRef，暂时传null
    handleFileUpload(firstFile, fileList, null);
  };

  // 处理Prompt选择
  const handlePromptSelect = (info: { data: any }) => {
    const label = info.data.label;
    if (typeof label === 'string') {
      handleSubmit(label);
    }
  };

  // 处理建议选择
  const handleSuggestionSelect = (itemVal: string) => {
    inputValue.value = `[${itemVal}]:`;
  };

  // 处理同步数据
  const handleSyncData = (content: string, dataTypes?: string[]) => {
    console.log('触发同步数据，发送sync_company_month_data_request消息', {
      dataTypes,
    });

    // 手动添加用户消息到聊天记录
    aiChatStore.addMessage({
      content,
      status: 'success',
      type: 'user',
    });

    // 滚动到底部
    scrollToBottom();

    // 发送sync_company_month_data_request消息
    const username = userStore.userInfo?.username || '';
    const syncMessage = {
      client_type: 'web',
      data: {
        company_name: selectedCompany.value,
        data_types: dataTypes || ['ALL'], // 使用传入的dataTypes，默认为['ALL']
        month: selectedMonth.value,
        username,
      },
      type: 'sync_company_month_data_request',
    };

    console.log('发送同步数据消息:', syncMessage);
    wsSend(JSON.stringify(syncMessage));
  };

  // 处理同步科目和辅助核算配置
  const handleSyncSubjectConfig = (content: string) => {
    console.log(
      '触发同步科目和辅助核算配置，发送sync_company_month_data_request消息',
    );

    // 手动添加用户消息到聊天记录
    aiChatStore.addMessage({
      content,
      status: 'success',
      type: 'user',
    });

    // 滚动到底部
    scrollToBottom();

    // 发送sync_company_month_data_request消息
    const username = userStore.userInfo?.username || '';
    const syncMessage = {
      client_type: 'web',
      data: {
        company_name: selectedCompany.value,
        data_types: ['科目配置', '辅助核算配置'],
        month: selectedMonth.value,
        username,
      },
      type: 'sync_company_month_data_request',
    };

    console.log('发送同步科目和辅助核算配置消息:', syncMessage);
    wsSend(JSON.stringify(syncMessage));
  };

  // 处理同步客户列表
  const handleSyncCustomerList = (content: string) => {
    console.log('触发同步客户列表，发送info_sync_request消息');

    // 手动添加用户消息到聊天记录
    aiChatStore.addMessage({
      content,
      status: 'success',
      type: 'user',
    });

    // 滚动到底部
    scrollToBottom();

    // 发送info_sync_request消息
    const username = userStore.userInfo?.username || '';
    const syncMessage = {
      client_type: 'web',
      data: {
        company_name: selectedCompany.value,
        data_source: '17dz',
        month: selectedMonth.value,
        type: 'user_company_list',
        username,
      },
      type: 'info_sync_request',
    };

    console.log('发送同步客户列表消息:', syncMessage);
    wsSend(JSON.stringify(syncMessage));
  };

  // 处理发票生成凭证
  const handleDbToVoucher = (content: string, types?: string[]) => {
    console.log('触发发票生成凭证，发送db_to_voucher_request消息', { types });

    // 手动添加用户消息到聊天记录
    aiChatStore.addMessage({
      content,
      status: 'success',
      type: 'user',
    });

    // 滚动到底部
    scrollToBottom();

    // 发送db_to_voucher_request消息
    const dbToVoucherMessage = {
      client_type: 'web',
      data: {
        company_name: selectedCompany.value,
        month: selectedMonth.value,
        types: types || ['销项发票', '进项专票', '一般进项'], // 使用传入的types，默认为全部类型
      },
      type: 'db_to_voucher_request',
    };

    console.log('发送db_to_voucher_request消息:', dbToVoucherMessage);
    wsSend(JSON.stringify(dbToVoucherMessage));
  };

  // 处理拉取发票源文件
  const handleInvoiceFileDownload = (invoiceType: string) => {
    const content = '同步发票源文件';
    console.log('触发同步发票源文件，发送origin_file_download_request消息', {
      invoiceType,
    });

    // 手动添加用户消息到聊天记录
    aiChatStore.addMessage({
      content,
      status: 'success',
      type: 'user',
    });

    // 滚动到底部
    scrollToBottom();

    // 发送origin_file_download_request消息
    const username = userStore.userInfo?.username || '';
    const invoiceFileDownloadMessage = {
      client_type: 'web',
      data: {
        company_name: selectedCompany.value,
        invoice_type: invoiceType, // 支持 "input_invoice|output_invoice" 格式
        month: selectedMonth.value,
      },
      type: 'origin_file_download_request',
    };

    console.log(
      '发送origin_file_download_request消息:',
      invoiceFileDownloadMessage,
    );
    wsSend(JSON.stringify(invoiceFileDownloadMessage));
  };

  // 处理同步银行回单
  const handleBankReceiptSync = () => {
    const content = '同步银行回单';
    console.log('触发同步银行回单，发送origin_file_download_request消息');

    // 手动添加用户消息到聊天记录
    aiChatStore.addMessage({
      content,
      status: 'success',
      type: 'user',
    });

    // 滚动到底部
    scrollToBottom();

    // 发送origin_file_download_request消息
    const bankReceiptSyncMessage = {
      client_type: 'web',
      data: {
        company_name: selectedCompany.value,
        invoice_type: 'bank_receipt',
        month: selectedMonth.value,
      },
      type: 'origin_file_download_request',
    };

    console.log('发送银行回单同步消息:', bankReceiptSyncMessage);
    wsSend(JSON.stringify(bankReceiptSyncMessage));
  };

  // 处理银行回单生成凭证
  const handleBankReceiptToVoucher = (content: string) => {
    console.log('触发银行回单生成凭证，发送db_to_voucher_request消息');

    // 手动添加用户消息到聊天记录
    aiChatStore.addMessage({
      content,
      status: 'success',
      type: 'user',
    });

    // 滚动到底部
    scrollToBottom();

    // 发送db_to_voucher_request消息
    const bankReceiptToVoucherMessage = {
      client_type: 'web',
      data: {
        company_name: selectedCompany.value,
        month: selectedMonth.value,
        types: ['银行回单'], // 银行回单类型
      },
      type: 'db_to_voucher_request',
    };

    console.log('发送银行回单生成凭证消息:', bankReceiptToVoucherMessage);
    wsSend(JSON.stringify(bankReceiptToVoucherMessage));
  };

  // 处理凭证合并
  const handleMergeVouchers = async (content: string) => {
    console.log('触发凭证合并功能');

    // 手动添加用户消息到聊天记录
    aiChatStore.addMessage({
      content,
      status: 'success',
      type: 'user',
    });

    // 滚动到底部
    scrollToBottom();

    try {
      // 调用凭证合并API
      const { generalMergeVouchers } = await import('#/api/jsj-ai/api-v2');

      const result = await generalMergeVouchers({
        company_name: selectedCompany.value,
        month: selectedMonth.value,
      });

      if (result.success) {
        // 添加成功消息
        aiChatStore.addMessage({
          content: '凭证合并成功！正在跳转到查看凭证页面...',
          status: 'success',
          type: 'ai',
        });

        // 延迟跳转，让用户看到成功消息
        setTimeout(() => {
          // 跳转到查看凭证页面
          const currentRoute = router.currentRoute.value;

          if (currentRoute.path === '/bookkeeping/view') {
            // 如果已经在查看凭证页面，刷新页面
            window.location.reload();
          } else {
            // 否则跳转到查看凭证页面
            router.push('/bookkeeping/view');
          }
        }, 1500);
      } else {
        // 添加失败消息
        aiChatStore.addMessage({
          content: `凭证合并失败：${result.message}`,
          status: 'error',
          type: 'ai',
        });
      }
    } catch (error: any) {
      console.error('凭证合并失败:', error);

      // 添加错误消息
      aiChatStore.addMessage({
        content: `凭证合并失败：${error?.message || '未知错误'}`,
        status: 'error',
        type: 'ai',
      });
    }
  };

  // 显示消息详情
  const showMessageDetail = (message: ChatMessage) => {
    selectedMessage.value = message;
    detailModalVisible.value = true;
  };

  // 重新加载页面
  const reloadPage = () => {
    window.location.reload();
  };

  // 设置WebSocket消息处理
  const setupWebSocketMessageHandler = () => {
    // 这里需要设置WebSocket的onMessage处理器
    // 由于useWebSocketConnection可能需要修改来支持外部消息处理器
    // 暂时保留这个结构
  };

  onMounted(async () => {
    console.log('AI聊天组件开始初始化...');

    try {
      // 设置WebSocket连接成功后的回调
      setOnConnectedCallback(() => {
        console.log('WebSocket连接成功，发送更新公司信息消息');
        sendUpdateClientInfoMessage(selectedCompany.value, selectedMonth.value);
      });

      // 打开WebSocket连接
      wsOpen();
      console.log('WebSocket连接已启动');

      // 设置消息处理器
      setupWebSocketMessageHandler();
    } catch (error) {
      console.error('WebSocket连接失败:', error);
    }

    // 监听消息列表变化，自动滚动到底部
    watch(
      messages,
      () => {
        // scrollToBottom 内部已经有 nextTick，这里直接调用
        scrollToBottom();
      },
      { deep: true },
    );

    // 监听全局公司和月份变化，发送更新消息
    watch(
      [selectedCompany, selectedMonth],
      ([newCompany, newMonth], [oldCompany, oldMonth]) => {
        // 只有在值真正发生变化时才处理
        if (
          newCompany &&
          newMonth &&
          (newCompany !== oldCompany || newMonth !== oldMonth)
        ) {
          console.log('全局公司或月份变化，发送更新消息:', {
            from: { company: oldCompany, month: oldMonth },
            to: { company: newCompany, month: newMonth },
          });

          // 如果WebSocket已连接，立即发送更新消息
          if (connectionStatus.value.isConnected) {
            sendUpdateClientInfoMessage(newCompany, newMonth);
          } else {
            console.log('WebSocket未连接，将在连接成功后发送更新消息');
          }
        }
      },
      { immediate: false },
    );

    // 初始化完成
    console.log('AI聊天组件初始化完成');
  });

  onUnmounted(() => {
    wsClose();
  });

  // 暴露方法给父组件调用
  defineExpose({
    handleBankReceiptSync,
    handleBankReceiptToVoucher,
    handleDbToVoucher,
    handleInvoiceFileDownload,
    handleSyncCustomerList,
    handleSyncData,
    handleSyncSubjectConfig,
  });
</script>

<template>
  <div class="chat-container">
    <div class="chat-card">
      <!-- 聊天头部 -->
      <ChatHeader
        :connection-status="connectionStatus"
        @reconnect="reconnectWebSocket"
      />

      <div class="chat-layout">
        <div class="chat-main">
          <!-- 消息容器 -->
          <div class="messages-container">
            <ChatMessages
              :messages="messages"
              :loading="loading"
              :is-initializing="isInitializing"
              :initialization-error="initializationError"
              @show-message-detail="showMessageDetail"
              @reload-page="reloadPage"
            />
          </div>

          <!-- 输入容器 -->
          <div class="input-container">
            <ChatInput
              v-model:input-value="inputValue"
              v-model:file-upload-open="fileUploadOpen"
              :loading="loading"
              :uploaded-files="uploadedFiles"
              :file-items="fileItems"
              @submit="handleSubmit"
              @file-change="handleFileChange"
              @file-upload="handleFileUploadWrapper"
              @prompt-select="handlePromptSelect"
              @suggestion-select="handleSuggestionSelect"
              @sync-data="handleSyncData"
              @sync-subject-config="handleSyncSubjectConfig"
              @sync-customer-list="handleSyncCustomerList"
              @db-to-voucher="handleDbToVoucher"
              @merge-vouchers="handleMergeVouchers"
              @invoice-file-download="handleInvoiceFileDownload"
              @bank-receipt-sync="handleBankReceiptSync"
              @bank-receipt-to-voucher="handleBankReceiptToVoucher"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 详情模态框 -->
    <MessageDetailModal
      v-model:visible="detailModalVisible"
      :message="selectedMessage"
    />
  </div>
</template>

<style scoped lang="scss">
  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }

    50% {
      opacity: 0.7;
      transform: scale(1.2);
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .chat-container {
    height: 100%;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }

  .chat-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgb(0 0 0 / 10%);

    :deep(.ant-card-body) {
      height: 100%;
      padding: 0;
    }
  }

  .chat-layout {
    display: flex;
    flex: 1;
    height: calc(100% - 68px);
    overflow: hidden;
  }

  .chat-main {
    display: flex;
    flex: 1;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  .messages-container {
    flex: 1;
    padding: 8px;
    overflow-y: auto;
    background: rgb(255 255 255 / 80%);
    backdrop-filter: blur(10px);

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, #667eea, #764ba2);
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, #5a67d8, #6b46c1);
      }
    }

    &::-webkit-scrollbar-track {
      background: rgb(255 255 255 / 10%);
      border-radius: 6px;
    }
  }

  .input-container {
    padding: 8px 16px;
    background: rgb(255 255 255 / 95%);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgb(255 255 255 / 20%);
    box-shadow: 0 -4px 16px rgb(0 0 0 / 5%);
  }
</style>
