export const ActionTypes = {
  MERGE_VOUCHERS: '凭证合并',
  INVOICE_TO_VOUCHER: '发票生成凭证',
  SYNC_INVOICE_DATA: '同步发票',
  SYNC_INVOICE_FILES: '同步发票源文件',
  SYNC_CUSTOMER_LIST: '同步客户列表',
  SYNC_SUBJECT_CONFIG: '同步科目配置',
  SYNC_BANK_RECEIPT: '同步银行回单',
  BANK_RECEIPT_TO_VOUCHER: '回单生成凭证',
  AUDIT_REPORT: '审计报告',
  GENERATE_REPORT: '报表生成',
  DATA_BACKUP: '数据备份',
  TAX_DECLARATION: '税务申报',
} as const;

export type ActionType = typeof ActionTypes[keyof typeof ActionTypes];

// 定义每个动作对应的处理函数映射
export interface ActionHandler {
  event: string;
  payload?: string | { data: { action: string; label: string } };
}

export const ActionHandlers: Record<ActionType, ActionHandler> = {
  [ActionTypes.MERGE_VOUCHERS]: {
    event: 'mergeVouchers',
    payload: '凭证合并',
  },
  [ActionTypes.INVOICE_TO_VOUCHER]: {
    event: 'dbToVoucher',
    payload: '发票生成凭证',
  },
  [ActionTypes.SYNC_INVOICE_DATA]: {
    event: 'syncData',
    payload: '请同步当前公司的最新数据',
  },
  [ActionTypes.SYNC_INVOICE_FILES]: {
    event: 'invoiceFileDownload',
    payload: 'input_invoice|output_invoice',
  },
  [ActionTypes.SYNC_CUSTOMER_LIST]: {
    event: 'syncCustomerList',
    payload: '同步客户列表',
  },
  [ActionTypes.SYNC_SUBJECT_CONFIG]: {
    event: 'syncSubjectConfig',
    payload: '同步科目和辅助核算配置',
  },
  [ActionTypes.SYNC_BANK_RECEIPT]: {
    event: 'bankReceiptSync',
  },
  [ActionTypes.BANK_RECEIPT_TO_VOUCHER]: {
    event: 'bankReceiptToVoucher',
    payload: '银行回单生成凭证',
  },
  [ActionTypes.AUDIT_REPORT]: {
    event: 'promptSelect',
    payload: { data: { action: 'audit', label: '审计报告' } },
  },
  [ActionTypes.GENERATE_REPORT]: {
    event: 'promptSelect',
    payload: { data: { action: 'report', label: '报表生成' } },
  },
  [ActionTypes.DATA_BACKUP]: {
    event: 'promptSelect',
    payload: { data: { action: 'backup', label: '数据备份' } },
  },
  [ActionTypes.TAX_DECLARATION]: {
    event: 'promptSelect',
    payload: { data: { action: 'tax', label: '税务申报' } },
  },
}; 
