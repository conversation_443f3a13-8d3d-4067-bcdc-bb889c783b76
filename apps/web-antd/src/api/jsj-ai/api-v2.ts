import type {
  AccountSubjectQueryParams,
  AccountSubjectResponseData,
  AssistantAccountingQueryParams,
  AssistantAccountingResponseData,
  BankReceiptData,
  BankReceiptQueryParams,
  BankReceiptUpdateSceneParams,
  BatchUpdateResponse,
  CompaniesQueryParams,
  CompanyData,
  InvoiceData,
  InvoiceQueryParams,
  InvoiceUpdateSceneParams,
  PayrollData,
  PayrollQueryParams,
  ScenarioConditionAddParams,
  ScenarioConditionConfigData,
  ScenarioConditionConfigQueryParams,
  ScenarioConditionData,
  ScenarioConditionDeleteParams,
  ScenarioConditionQueryParams,
  ScenarioConditionUpdateParams,
  ScenarioEntryAddParams,
  ScenarioEntryData,
  ScenarioEntryDeleteParams,
  ScenarioEntryQueryParams,
  ScenarioEntryUpdateParams,
  UpdateAutoModeParams,
  VoucherPdfGenerateParams,
  VoucherQueryParams,
  VoucherSourceDataQueryParams,
  UpdateVoucherParams,
  MergeVouchersParams,
  GeneralMergeVouchersParams,
  WriteBackVouchersParams,
  UserCustomerNamesQueryParams,
  UserCustomerNamesResponseData,
  DeleteVouchersParams,
} from './types';

import { httpClient, apiCall, type ApiResponse } from './request';

// ==================== 查询类API ====================

/**
 * 查询发票列表
 */
export async function fetchInvoiceList(params: InvoiceQueryParams): Promise<InvoiceData[]> {
  return apiCall(() => httpClient.get<ApiResponse<InvoiceData[]>>('/invoice/list', { params }));
}

/**
 * 查询银行回单列表
 */
export async function fetchBankReceiptList(params: BankReceiptQueryParams): Promise<BankReceiptData[]> {
  return apiCall(() => httpClient.get<ApiResponse<BankReceiptData[]>>('/bank_receipts/list', { params }));
}

/**
 * 查询工资单列表
 */
export async function fetchPayrollList(params: PayrollQueryParams): Promise<PayrollData[]> {
  return apiCall(() => httpClient.get<ApiResponse<PayrollData[]>>('/payroll/list', { params }));
}

/**
 * 查询场景分录列表
 */
export async function fetchScenarioEntryList(params: ScenarioEntryQueryParams): Promise<ScenarioEntryData[]> {
  return apiCall(() => httpClient.get<ApiResponse<ScenarioEntryData[]>>('/scenario_entry/list', { params }));
}

/**
 * 更新场景条目
 */
export async function updateScenarioEntry(params: ScenarioEntryUpdateParams) {
  return apiCall(() => httpClient.post<ApiResponse<any>>('/scenario_entry/update', params));
}

/**
 * 查询场景条件列表
 */
export async function fetchScenarioConditionList(params: ScenarioConditionQueryParams): Promise<ScenarioConditionData[]> {
  return apiCall(() => httpClient.get<ApiResponse<ScenarioConditionData[]>>('/scenario_condition/list', { params }));
}

/**
 * 添加场景条件
 */
export async function addScenarioCondition(params: ScenarioConditionAddParams) {
  return apiCall(() => httpClient.post<ApiResponse<any>>('/accounting_scenario_condition/add', params));
}

/**
 * 添加场景分录
 */
export async function addScenarioEntry(params: ScenarioEntryAddParams) {
  return apiCall(() => httpClient.post<ApiResponse<any>>('/scenario_entry/add', params));
}

/**
 * 批量更新发票场景
 */
export async function updateInvoiceScene(params: InvoiceUpdateSceneParams[]): Promise<BatchUpdateResponse> {
  return apiCall(() => httpClient.post<ApiResponse<BatchUpdateResponse>>('/invoice_service/update_scene', params));
}

/**
 * 批量更新银行流水场景
 */
export async function updateBankReceiptScene(params: BankReceiptUpdateSceneParams[]): Promise<BatchUpdateResponse> {
  return apiCall(() => httpClient.post<ApiResponse<BatchUpdateResponse>>('/bank_receipt/update_scene', params));
}

/**
 * 查询科目列表
 */
export async function fetchAccountSubjectList(params: AccountSubjectQueryParams): Promise<AccountSubjectResponseData> {
  return apiCall(() => httpClient.get<ApiResponse<AccountSubjectResponseData>>('/account-subjects/list', { params }));
}

/**
 * 查询辅助核算列表
 */
export async function fetchAssistantAccountingList(params: AssistantAccountingQueryParams): Promise<AssistantAccountingResponseData> {
  return apiCall(() => httpClient.get<ApiResponse<AssistantAccountingResponseData>>('/assistant-accounting/list', { params }));
}


/**
 * 删除场景条件
 */
export async function deleteScenarioCondition(params: ScenarioConditionDeleteParams) {
  return apiCall(() => httpClient.get<ApiResponse<any>>('/scenario_condition/delete', { params }));
}

/**
 * 更新场景条件
 */
export async function updateScenarioCondition(params: ScenarioConditionUpdateParams) {
  return apiCall(() => httpClient.post<ApiResponse<any>>('/scenario_condition/update', params));
}

/**
 * 删除场景分录
 */
export async function deleteScenarioEntry(params: ScenarioEntryDeleteParams) {
  return apiCall(() => httpClient.get<ApiResponse<any>>('/scenario_entry/delete', { params }));
}

/**
 * 查询公司列表
 */
export async function fetchCompaniesList(params: CompaniesQueryParams): Promise<CompanyData[]> {
  const response: any = await apiCall(() => httpClient.get<ApiResponse<{ companies: CompanyData[] }>>('/companies/list', { params }));
  return response.companies || [];
}

/**
 * 更新AI自动模式
 */
export async function updateAutoMode(params: UpdateAutoModeParams) {
  return apiCall(() => httpClient.post<ApiResponse<any>>('/companies/update-auto-mode', params));
}

/**
 * 生成凭证PDF
 */
export async function generateVoucherPdf(params: VoucherPdfGenerateParams) {
  return apiCall(() => httpClient.post<ApiResponse<any>>('/voucher/generate_pdf', params));
}

/**
 * 获取当前公司某个月份的凭证信息
 */
export async function getCurrentVouchers(params: VoucherQueryParams) {
  return apiCall(() => httpClient.get<ApiResponse<any>>('/vouchers/current', { params }));
}

/**
 * 获取凭证原始数据
 */
export async function getVoucherSourceData(params: VoucherSourceDataQueryParams) {
  return apiCall(() => httpClient.get<ApiResponse<any>>('/vouchers/source-data', { params }));
}

// ==================== 凭证操作类API ====================

/**
 * 修改凭证信息
 */
export async function updateVoucher(data: UpdateVoucherParams) {
  return apiCall(() => httpClient.put<ApiResponse<any>>('/vouchers/update', data));
}

/**
 * 合并凭证
 */
export async function mergeVouchers(data: MergeVouchersParams) {
  return apiCall(() => httpClient.post<ApiResponse<any>>('/vouchers/merge', data));
}

/**
 * 通用凭证合并
 */
export async function generalMergeVouchers(data: GeneralMergeVouchersParams) {
  return apiCall(() => httpClient.post<ApiResponse<any>>('/vouchers/general_merge', data));
}

/**
 * 凭证写入
 */
export async function writeBackVouchers(data: WriteBackVouchersParams) {
  return apiCall(() => httpClient.post<ApiResponse<any>>('/vouchers/write_back', data));
}

/**
 * 获取用户负责的客户名称列表
 */
export async function getUserCustomerNames(params: UserCustomerNamesQueryParams): Promise<UserCustomerNamesResponseData> {
  return apiCall(() => httpClient.get<ApiResponse<UserCustomerNamesResponseData>>('/users/get_user_info', { params }));
}

/**
 * 查询场景条件配置
 */
export async function fetchScenarioConditionConfig(params: ScenarioConditionConfigQueryParams): Promise<ScenarioConditionConfigData[]> {
  return apiCall(() => httpClient.get<ApiResponse<ScenarioConditionConfigData[]>>('/config/get_by_type', { params }));
}

/**
 * 删除凭证
 */
export async function deleteVouchers(data: DeleteVouchersParams) {
  return apiCall(() => httpClient.delete<ApiResponse<any>>('/vouchers/delete', { data }));
}
