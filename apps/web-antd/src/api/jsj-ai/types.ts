// 原始发票相关的类型定义文件

// 发票查询参数接口
export interface InvoiceQueryParams {
  company_name: string; // 公司名称，必填
  input_output: 'input' | 'output'; // 进项、销项，必填
  begin_time?: string; // 开始时间，如2025-01-01，选填
  end_time?: string; // 结束时间，如2025-01-22，选填
  status?: string; // 状态，选填
  voucher_num?: string; // 凭证编号，选填
}

// 银行回单查询参数接口
export interface BankReceiptQueryParams {
  company_name: string; // 公司名称，必填
  begin_time?: string; // 开始时间，如2025-01-01，选填
  end_time?: string; // 结束时间，如2025-01-22，选填
  month?: string; // 月份，如202502，选填
  type?: string; // 收支类型，选填
  voucher_num?: string; // 凭证编号，选填
}

// 工资单查询参数接口
export interface PayrollQueryParams {
  company_name: string; // 公司名称，必填
  month: string; // 月份，如202502，必填
  id_number?: string; // 身份证，选填
  name?: string; // 姓名，选填
  voucher_num?: string; // 凭证编号，选填
}

// 发票数据接口（根据实际API响应更新）
export interface InvoiceData {
  _id: string;
  digital_invoice_number: string; // 发票号
  voucher_num: string; // 凭证号
  voucher_id: string; // 凭证ID
  status: string; // 状态
  type: string; // 发票类型
  issue_date: string; // 开票日期
  goods_name: string; // 货物或劳务明细
  seller_name: string; // 销方名称
  buyer_name: string; // 购方名称
  tax_rate: string; // 税率
  total_tax: number; // 税额
  total_amount: number; // 金额
  total: number; // 价税合计
  remark: string; // 备注
  scene?: string; // 场景
  month?: string; // 月份，如202502
}

// 银行回单数据接口
export interface BankReceiptData {
  _id: string;
  company_name: string; // 公司名称
  timestamp: string;
  month: string; // 月份
  type: string; // 收支类型
  scene?: string; // AI记账场景
  voucher_id: string;
  voucher_num?: string; // 凭证号
  transaction_id: string; // 回单编号（交易流水号）
  transaction_time: string; // 交易日期
  account_number: string;
  account_name: string; // 账户名称
  bank_name: string; // 银行名称
  counterparty_account_number: string | null;
  counterparty_account_name: string; // 对方户名称
  counterparty_bank_name: string | null;
  amount: number; // 金额
  balance?: number; // 余额
  currency: string; // 币种
  summary: string; // 摘要
  note: string | null; // 备注
  desc: string; // 附言
  url: string;
  sequence_number: number;
  source_file: string;
  created_at: string;
  updated_at: string;
}

// 工资单数据接口
export interface PayrollData {
  _id: string;
  name: string; // 姓名
  employee_id: string; // 员工编号
  basic_salary: number; // 基本工资
  allowances: number; // 津贴
  deductions: number; // 扣除
  social_insurance_deduction: number; // 社保扣除
  housing_fund_deduction: number; // 公积金扣除
  income_tax: number; // 个人所得税
  net_salary: number; // 实发工资
  work_days: number; // 工作天数
  overtime_hours: number; // 加班小时
  id_number: string; // 身份证号
  remark: string; // 备注
  company_name: string; // 公司名称
  month: string; // 月份
  timestamp: number;
  actual_salary: number; // 实际工资
  total_salary: number; // 总工资
  base_salary: number; // 基础工资
  performance_salary: number; // 绩效工资
  position_salary: number; // 岗位工资
  taxable_salary: number; // 应税工资
  pension_personal: number; // 个人养老保险
  medical_personal: number; // 个人医疗保险
  unemployment_personal: number; // 个人失业保险
  housing_fund_personal: number; // 个人公积金
  pension_company: number; // 公司养老保险
  medical_company: number; // 公司医疗保险
  unemployment_company: number; // 公司失业保险
  housing_fund_company: number; // 公司公积金
  extra_housing_fund: number; // 补充公积金
  extra_medical: number; // 补充医疗
  created_at: string;
  updated_at: string;
}

// 业务层API响应接口（内层数据）
export interface BusinessApiResponse<T> {
  status: string;
  message: string;
  data: T[];
}

// 公司列表专用的业务层API响应接口
export interface CompaniesBusinessApiResponse {
  status: string;
  message: string;
  data: CompaniesResponseData;
}

// 完整的API响应接口（包含axios响应信息）
export interface FullApiResponse<T> {
  data: BusinessApiResponse<T>; // 业务数据
  status: number; // HTTP状态码
  statusText: string; // HTTP状态文本
  headers: Record<string, any>; // 响应头
  config: Record<string, any>; // 请求配置
  request?: any; // 请求对象
}

// 公司列表专用的完整API响应接口
export interface CompaniesFullApiResponse {
  data: CompaniesBusinessApiResponse; // 业务数据
  status: number; // HTTP状态码
  statusText: string; // HTTP状态文本
  headers: Record<string, any>; // 响应头
  config: Record<string, any>; // 请求配置
  request?: any; // 请求对象
}

// 兼容旧版本的API响应接口
export interface ApiResponse<T> {
  status: string;
  message: string;
  data: T[];
}

// 场景条目详情接口
export interface ScenarioEntryDetail {
  subject: string; // 科目
  direction: '借' | '贷'; // 方向：借或贷
  source: string; // 来源
  negative?: string; // 正负标识：1 为负数，其他都为正数
}

// 场景条目查询参数接口
export interface ScenarioEntryQueryParams {
  company_name: string; // 公司名称，必填
  scene?: string; // 场景，选填
  type?: string; // 类型：进项发票、销项发票、银行回单，选填
  status?: 1; // 状态：1 表示只需要启用的场景，选填
  needDefault?: 1; // 是否需要默认的场景分录信息：1 表示需要，选填
}

// 场景条目数据接口
export interface ScenarioEntryData {
  _id: string;
  company_name: string; // 公司名称
  scene: string; // 场景
  type: string; // 类型：进项发票、销项发票、银行回单
  status: 0 | 1; // 状态：0 禁用，1 启用
  detail: ScenarioEntryDetail[]; // 详情数组
  created_at: string; // 创建时间
  updated_at: string; // 更新时间
}

// 场景条目更新参数接口
export interface ScenarioEntryUpdateParams {
  _id: string;
  company_name: string; // 公司名称，必填
  scene: string; // 场景，必填
  type: string; // 类型：进项发票、销项发票、银行回单，必填
  status: '0' | '1'; // 状态：0 禁用，1 启用（字符串格式用于API请求）
  detail: ScenarioEntryDetail[]; // 详情数组
}

// 场景条目更新响应接口
export interface ScenarioEntryUpdateResponse {
  status: 'success' | 'error';
  message: string;
}

// 场景条件项接口
export interface ScenarioConditionItem {
  name: string; // 条件名称
  value: string; // 条件值
}

// 场景条件查询参数接口
export interface ScenarioConditionQueryParams {
  company_name: string; // 公司名称，必填
}

// 场景条件数据接口
export interface ScenarioConditionData {
  _id: string;
  company_name: string; // 公司名称
  scene: string; // 场景
  type: string; // 类型：进项发票、销项发票、银行回单
  condition: ScenarioConditionItem[]; // 条件数组
  created_at: string; // 创建时间
  updated_at: string; // 更新时间
}

// 场景条件添加参数接口
export interface ScenarioConditionAddParams {
  company_name: string; // 公司名称，必填
  scene: string; // 场景，必填
  type: string; // 类型：进项发票、销项发票、银行回单，必填
  condition: ScenarioConditionItem[]; // 条件数组
}

// 场景条件添加响应接口
export interface ScenarioConditionAddResponse {
  status: 'success' | 'error';
  condition_id: string;
  message: string;
}

// 场景条件删除参数接口
export interface ScenarioConditionDeleteParams {
  id: string; // 场景条件ID，必填
}

// 场景条件删除响应接口
export interface ScenarioConditionDeleteResponse {
  status: 'success' | 'fail';
  message: string;
}

// 场景条件更新参数接口
export interface ScenarioConditionUpdateParams {
  _id: string; // 场景条件ID，必填
  company_name: string; // 公司名称，必填
  scene: string; // 场景，必填
  type: string; // 类型：进项发票、销项发票、银行回单，必填
  condition: ScenarioConditionItem[]; // 条件数组
}

// 场景条件更新响应接口
export interface ScenarioConditionUpdateResponse {
  status: 'success' | 'fail';
  message: string;
}

// 场景条件配置详情接口
export interface ScenarioConditionConfigDetail {
  name: string; // 条件名称
  values?: string[]; // 可选值数组（如果有固定选项）
}

// 场景条件配置数据接口
export interface ScenarioConditionConfigData {
  _id: string;
  config_type: string; // 配置类型，固定为 "scene_condition"
  config_key: string; // 配置键，如 "进项发票"、"销项发票"、"银行回单"
  config_value: string; // 配置值，与 config_key 相同
  details: ScenarioConditionConfigDetail[]; // 详情数组
  created_at: string; // 创建时间
  updated_at: string; // 更新时间
}

// 场景条件配置查询参数接口
export interface ScenarioConditionConfigQueryParams {
  config_type: string; // 配置类型，固定为 "scene_condition"
}

// 场景条件配置响应接口
export interface ScenarioConditionConfigResponse {
  status: 'success' | 'error';
  message: string;
  data: ScenarioConditionConfigData[];
}

// 场景分录添加参数接口
export interface ScenarioEntryAddParams {
  company_name: string; // 公司名称，必填
  scene: string; // 场景，必填
  type: string; // 类型：进项发票、销项发票、银行回单，必填
  status: '0' | '1'; // 状态：0 禁用，1 启用
  detail: ScenarioEntryDetail[]; // 详情数组
}

// 场景分录添加响应接口
export interface ScenarioEntryAddResponse {
  status: 'success' | 'error';
  entry_id: string;
  message: string;
}

// 场景分录删除参数接口
export interface ScenarioEntryDeleteParams {
  id: string; // 场景分录ID，必填
}

// 场景分录删除响应接口
export interface ScenarioEntryDeleteResponse {
  status: 'success' | 'fail';
  message: string;
}

// 发票更新场景参数接口
export interface InvoiceUpdateSceneParams {
  id: string; // 发票ID
  type: 'input_invoice' | 'output_invoice'; // 发票类型：进项发票、销项发票
  scene: string; // 场景
  company_name: string; // 公司名称
  ii_type: 'normal' | 'vat'; // 发票类型：normal 普通发票, vat 专用发票
  ii_seller: string; // 销方名称（为进项发票时填写）
  ii_buyer: string; // 购方名称（为销项发票时填写）
  ii_goods: string; // 货物或劳务明细
  ii_tax_rates: number; // 税率（保留2位小数）
  ii_note: string; // 备注
}

// 银行流水更新场景参数接口
export interface BankReceiptUpdateSceneParams {
  id: string; // 银行流水ID
  type: 'bank_receipt'; // 类型
  scene: string; // 场景
  company_name: string; // 公司名称
  br_type: string; // 收支类型
  br_account_name: string; // 我方名称
  br_counterparty_account_name: string; // 对方名称
  br_currency: string; // 币种
  br_summary: string; // 摘要
  br_note: string; // 附言 + 备注
}

// 批量更新响应接口
export interface BatchUpdateResponse {
  status: 'success' | 'error';
  message: string;
}

// 科目查询参数接口
export interface AccountSubjectQueryParams {
  company_name: string; // 公司名称，必填
}

// 科目数据接口
export interface AccountSubjectItem {
  openingBalance: number | null;
  id: number;
  type: string;
  code: string;
  name: string;
  fullName: string;
  direction: number;
  pCode: string;
  pId: number | null;
  level: number;
  last: boolean; // 是否为最底级科目
  assistantType: string; // 辅助核算类型：s-供应商, c-客户, i-存货, p-项目, d-部门, e-员工
  auxiliaryTypes: string[] | null;
  status: string;
  freezeStatus: string | null;
  source: string;
  accountSetId: number;
  unit: string | null;
  fcurCode: string | null;
  useAssistant: boolean; // 是否启用辅助核算
  useQuantity: boolean;
  useFcur: boolean;
  customerId: number;
  remark: string | null;
  children: AccountSubjectItem[];
  assistants: any[];
  auxiliaryItems: any;
  pinYinInitial: string | null;
  inventoryTypes: any;
  logicalUseQuantity: boolean;
}

// 科目分类数据接口
export interface AccountSubjectsData {
  assets: AccountSubjectItem[]; // 资产类
  cost: AccountSubjectItem[]; // 成本类
  equity: AccountSubjectItem[]; // 权益类
  liabilities: AccountSubjectItem[]; // 负债类
  profit: AccountSubjectItem[]; // 损益类
}

// 科目列表响应数据接口
export interface AccountSubjectResponseData {
  result: string;
  subjects: AccountSubjectsData;
  total_count: number;
  company_name: string;
}

// 科目列表响应接口
export interface AccountSubjectResponse {
  status: 'success' | 'error';
  message: string;
  data: AccountSubjectResponseData;
  timestamp: string;
  instance_id: string;
}

// 辅助核算查询参数接口
export interface AssistantAccountingQueryParams {
  company_name: string; // 公司名称，必填
}

// 辅助核算项目接口
export interface AssistantAccountingItem {
  id: number;
  code: string;
  name: string;
  freezeStatus: string;
  accountSetId: number;
  customerId: number;
  remark: string | null;
  type: string | null;
  fullName: string | null;
  fullNameWithSpace: string | null;
  fullNameExtend: string | null;
  pinYinInitial: string | null;
  taxpayerNum: string | null;
  repeatCode: number;
  titles: any[];
  accountTitleId: number | null;
}

// 辅助核算数据接口
export interface AssistantAccountingData {
  customer: AssistantAccountingItem[]; // 客户
  project?: AssistantAccountingItem[]; // 项目
  invoice_project?: AssistantAccountingItem[]; // 发票项目
  inventory: AssistantAccountingItem[]; // 库存
  supplier: AssistantAccountingItem[]; // 供应商
  department?: AssistantAccountingItem[]; // 部门
  employee?: AssistantAccountingItem[]; // 员工
}

// 辅助核算响应数据接口
export interface AssistantAccountingResponseData {
  result: string;
  assistant_accounting: AssistantAccountingData;
  total_count: number;
  company_name: string;
}

// 辅助核算响应接口
export interface AssistantAccountingResponse {
  status: 'success' | 'error';
  message: string;
  data: AssistantAccountingResponseData;
  timestamp: string;
  instance_id: string;
}

// 表单值接口
export interface FormValues {
  dateRange?: [string, string];
  company_name?: string;
  voucher_num?: string;
  status?: string;
  month?: string;
  type?: string;
}

// 公司列表查询参数接口
export interface CompaniesQueryParams {
  account_name: string; // 登录用户名，必填
  month?: string; // 月份，格式YYYYMM，选填
  refresh?: number; // 是否刷新数据，选填
}

// 发票数据状态接口定义（销项发票和进项发票通用）
export interface InvoiceDataStatus {
  /** 状态 - 数据处理状态 */
  status: string;
  /** 数量 - 发票张数 */
  num: number;
  /** 详情数量 - 发票明细条数 */
  details_num: number;
  /** 总金额 - 不含税金额 */
  total_amount: number;
  /** 发票数量 - 发票张数（与num相同） */
  count: number;
  /** 已更新数量 - 已处理的发票数量 */
  updated_count: number;
  /** 税额 - 税金总额 */
  tax_amount: number;
  /** 价税合计 - 含税总金额 */
  total_with_tax: number;
}

// 银行回单数据状态接口定义
export interface BankReceiptStatus {
  /** 状态 - 数据处理状态 */
  status: string;
  /** 数量 - 银行回单数量 */
  num: number;
  /** 详情数量 - 银行回单明细条数 */
  details_num: number;
  /** 总金额 - 银行回单总金额 */
  total_amount: number;
  /** 凭证数量 - 生成的凭证数量 */
  voucher_num: number;
}

// 薪酬数据状态接口定义
export interface PayrollStatus {
  /** 状态 - 薪酬处理状态 */
  status: string;
  /** 员工数量 - 薪酬涉及的员工数量 */
  empolyee_num: number;
  /** 总薪酬 - 薪酬总额 */
  total_salary: number;
  /** 个人社保公积金 - 个人承担部分 */
  total_sbhjz_person: number;
  /** 公司社保公积金 - 公司承担部分 */
  total_sbhjz_company: number;
  /** 凭证数量 - 生成的凭证数量 */
  voucher_num: number;
}

// 报关单数据状态接口定义
export interface CustomsDeclarationStatus {
  /** 状态 - 报关单处理状态 */
  status: string;
  /** 数量 - 报关单数量 */
  num: number;
  /** 总金额 - 报关单总金额 */
  total_amount: number;
  /** 凭证数量 - 生成的凭证数量 */
  voucher_num: number;
}

// 公司配置接口定义
export interface CompanyConfigs {
  /** AI自动模式 - 自动处理模式设置 */
  auto_mode: 'autojob' | 'manual' | 'close';
}

// 公司信息接口定义
export interface CompanyData {
  /** 客户名称 - 公司的完整名称 */
  customerName: string;
  /** 客户编号 - 系统内部客户唯一标识 */
  customerCode?: string;
  /** 记账员 - 负责该公司记账的员工账号 */
  bookkeeper: string;
  /** 月份 - 数据所属月份，格式YYYYMM */
  month: string;
  /** 发票整理状态 - 理票进度状态 */
  invoiceArrangeStatus: string;
  /** 记账状态 - 会计记账处理状态 */
  accountStatus: string;
  /** 税务申报状态 - 税务申报处理状态 */
  taxDeclareStatus: string;
  /** 记账状态详情 - 记账处理的详细状态描述 */
  accountStatusDetail: string;
  /** 付款状态 - 税款缴纳状态 */
  paymentStatus: string;
  /** 清卡状态 - 税控设备清卡状态 */
  clearCardStatus: string;
  /** 抄税状态 - 税控设备抄税状态 */
  copyTaxStatus: string;
  /** 最新备注 - 该客户的最新处理备注信息 */
  latestNote: string | null;
  /** 置顶标志 - 是否为重要客户需要置顶显示 */
  topFlag: number;
  /** 公司类型 - 纳税人类型："小规模纳税人"或"一般纳税人" */
  company_type?: string;
  /** 销项发票 - 销项发票数据状态 */
  output_invoice: InvoiceDataStatus;
  /** 进项普票 - 进项普通发票数据状态 */
  input_invoice_general: InvoiceDataStatus;
  /** 进项专票 - 进项专用发票数据状态 */
  input_invoice_vat: InvoiceDataStatus;
  /** 银行回单 - 银行回单数据状态 */
  bank_receipt: BankReceiptStatus;
  /** 薪酬数据 - 薪酬处理状态 */
  payroll: PayrollStatus;
  /** 报关单 - 报关单数据状态 */
  customs_declaration_form: CustomsDeclarationStatus;
  /** 配置信息 - 公司相关配置 */
  configs: CompanyConfigs;
}

// 公司列表响应数据接口
export interface CompaniesResponseData {
  /** 公司列表 - 包含所有公司信息的数组 */
  companies: CompanyData[];
  /** 总数量 - 符合条件的公司总数 */
  total_count: number;
  /** 数据源 - 数据来源标识 */
  source?: string;
  /** 记账员 - 数据所属记账员 */
  bookkeeper?: string;
  /** 月份 - 数据所属月份 */
  month?: number;
  /** 刷新标志 - 是否为刷新数据 */
  refresh?: boolean;
}

// 公司列表响应接口
export interface CompaniesResponse {
  /** 响应状态 - API调用结果状态 */
  status: string;
  /** 响应消息 */
  message: string;
  /** 响应数据 - 包含公司列表和统计信息 */
  data: CompaniesResponseData;
  /** 时间戳 */
  timestamp: string;
  /** 实例ID */
  instance_id: string;
}

// 更新AI自动模式参数接口
export interface UpdateAutoModeParams {
  /** 公司名称 */
  company_name: string;
  /** AI自动模式 */
  auto_mode: 'autojob' | 'manual' | 'close';
}

// 更新AI自动模式响应接口
export interface UpdateAutoModeResponse {
  /** 响应状态 */
  status: string;
  /** 响应消息 */
  message?: string;
}

// 凭证PDF生成请求参数接口
export interface VoucherPdfGenerateParams {
  /** 凭证ID - 数据库中的唯一标识 */
  voucher_id: string;
}

// 凭证PDF生成响应接口
export interface VoucherPdfGenerateResponse {
  /** 响应结果 */
  result: string;
  /** PDF文件名 */
  file_name: string;
}

// 凭证查询参数接口
export interface VoucherQueryParams {
  /** 公司名称 */
  company_name: string;
  /** 月份 (YYYYMM格式) */
  month: string;
}

// 凭证项目接口
export interface VoucherItem {
  source_type: string;
  source_info: {
    bank_receipt_info?: {
      expense_transaction_num: number;
      income_transaction_num: number;
      timestamp: string;
      total_expense_amount: number;
      total_income_amount: number;
    };
    invoice_info?: {
      amount: number;
      fund_desc: string;
      id: string[];
      tax: number;
      timestamp: string;
      total: number;
    };
    payroll_info?: {
      timestamp: string;
      total_employee_deductions: number;
      total_employer_contributions: number;
      total_gross_salary: number;
    };
  };
  executor: string;
  confirmed: boolean;
  voucher: {
    details: Array<{
      account: string;
      credit: number;
      debit: number;
      id: number;
      summary: string;
    }>;
    id: number;
    record_date: string;
    timestamp: string;
    total_credit: number;
    total_debit: number;
    type: string;
    unique_id: string;
    write_back: boolean; // 写入状态，在voucher对象中
  };
}

// 凭证响应数据接口（业务层数据）
export interface VoucherResponseData {
  company_name: string;
  items: VoucherItem[];
  month: string;
}

// 凭证业务API响应接口（内层数据，遵循api-v2标准格式）
export interface VoucherBusinessApiResponse {
  status: string;
  message: string;
  data: VoucherResponseData;
}

// 凭证完整API响应接口（用于V2版本，遵循api-v2标准格式）
export interface VoucherFullApiResponse {
  data: VoucherBusinessApiResponse; // 业务数据
  status: number; // HTTP状态码
  statusText: string; // HTTP状态文本
  headers: Record<string, any>; // 响应头
  config: Record<string, any>; // 请求配置
  request?: any; // 请求对象
}

// 凭证原始数据查询参数接口
export interface VoucherSourceDataQueryParams {
  /** 公司名称 */
  company_name: string;
  /** 月份 (YYYYMM格式) */
  month: string;
  /** 凭证唯一ID */
  voucher_unique_id: string;
}

// 凭证原始数据响应接口
export interface VoucherSourceDataResponse {
  company_name: string;
  month: string;
  voucher_unique_id: string;
  source_info: {
    bank_receipt?: Array<{
      account_name: string;
      account_number: string;
      amount: number;
      bank_name: string;
      counterparty_account_name: string;
      counterparty_account_number: string;
      counterparty_bank_name: string;
      currency: string;
      id: string;
      note: string;
      summary: string;
      transaction_id: string;
      transaction_time: string;
      type: string;
    }>;
    input_invoice?: Array<{
      amount: number;
      buyer_name: string;
      buyer_tax_id: string;
      description: string;
      digital_invoice_number?: string;
      goods_name: string;
      id: string;
      invoice_code: string;
      invoice_number: string;
      is_positive: string;
      issue_date: string;
      issuer: string;
      quantity: number;
      risk_level: string;
      seller_name: string;
      seller_tax_id: string;
      source: string;
      specific_biz_type?: string;
      specification: string;
      status: string;
      tax_amount: number;
      tax_classification_code: string;
      tax_rate: number;
      total_amount: number;
      type: string;
      unit: string;
      unit_price: number;
    }>;
    output_invoice?: Array<{
      amount: number;
      buyer_name: string;
      buyer_tax_id: string;
      description: string;
      digital_invoice_number?: string;
      goods_name: string;
      id: string;
      invoice_code: string;
      invoice_number: string;
      is_positive: string;
      issue_date: string;
      issuer: string;
      quantity: number;
      risk_level: string;
      seller_name: string;
      seller_tax_id: string;
      source: string;
      specific_biz_type?: string;
      specification: string;
      status: string;
      tax_amount: number;
      tax_classification_code: string;
      tax_rate: number;
      total_amount: number;
      type: string;
      unit: string;
      unit_price: number;
    }>;
    payroll_info?: Array<{
      actual_salary: number;
      base_salary: number;
      employee_id: string;
      extra_housing_fund: number;
      extra_medical: number;
      housing_fund_company: number;
      housing_fund_personal: number;
      id_number: string;
      medical_company: number;
      medical_personal: number;
      name: string;
      pension_company: number;
      pension_personal: number;
      performance_salary: number;
      position_salary: number;
      remark: string;
      taxable_salary: number;
      total_salary: number;
      unemployment_company: number;
      unemployment_personal: number;
    }>;
  };
}

// 修改凭证请求参数接口
export interface UpdateVoucherParams {
  company_name: string;
  month: string;
  voucher_unique_id: string;
  voucher: {
    details: Array<{
      account: string;
      credit: number;
      debit: number;
      id: number;
      summary: string;
    }>;
    id: number;
    record_date: string;
    type: string;
  };
}

// 修改凭证响应接口
export interface UpdateVoucherResponse {
  result: string;
  timestamp: string;
  instance_id: string;
}

// 合并凭证请求参数接口
export interface MergeVouchersParams {
  company_name: string;
  month: string;
  voucher_unique_ids: string[];
}

// 合并凭证响应接口
export interface MergeVouchersResponse {
  result: string;
  err_msg: string | null;
  merged_info: {
    source_type: string;
    source_info: {
      bank_receipt_info: {
        merged_voucher_count: number;
        original_voucher_ids: string[];
        merge_timestamp: string;
        total_transactions: number;
        total_amount: number;
      };
    };
    executor: string;
    confirmed: boolean;
    voucher: {
      unique_id: string;
      timestamp: string;
      id: number;
      type: string;
      record_date: string;
      total_debit: number;
      total_credit: number;
      details: Array<{
        id: number;
        summary: string;
        account: string;
        debit: number;
        credit: number;
      }>;
    };
  };
}

// 凭证PDF生成完整API响应接口（包含axios响应信息）
export interface VoucherPdfFullApiResponse {
  data: VoucherPdfGenerateResponse; // 业务数据
  status: number; // HTTP状态码
  statusText: string; // HTTP状态文本
  headers: Record<string, any>; // 响应头
  config: Record<string, any>; // 请求配置
  request?: any; // 请求对象
}

// 通用凭证合并请求参数接口
export interface GeneralMergeVouchersParams {
  company_name: string;
  month: string;
}

// 通用凭证合并响应接口
export interface GeneralMergeVouchersResponse {
  result: string;
  message: string;
  timestamp: string;
}

// 凭证写入请求参数接口
export interface WriteBackVouchersParams {
  company_name: string;
  month: string;
  username: string;
  voucher_ids: string[];
}

// 凭证写入响应接口
export interface WriteBackVouchersResponse {
  result: string;
  message: string;
  timestamp: string;
  success_count?: number;
  failed_count?: number;
}

// 用户客户名称查询参数接口
export interface UserCustomerNamesQueryParams {
  /** 用户名 */
  username: string;
  /** 租户ID */
  tenant_id: string;
}

// 用户客户名称响应数据接口
export interface UserCustomerNamesResponseData {
  data_source: string;
  saas_id: string;
  yqdz: {
    company_id: string;
    account_id: string;
    customer_names: string[];
  };
}

// 删除凭证请求参数接口
export interface DeleteVouchersParams {
  /** 用户名 */
  username: string;
  /** 凭证ID数组 */
  ids: string[];
}

// 删除凭证响应接口
export interface DeleteVouchersResponse {
  result: string;
  message: string;
  deleted_count: number;
  timestamp: string;
}


