// API基础路径
const API_BASE_PATH = '/prod-api/autojob/api';

// 简化的响应类型
interface ApiResponse<T = any> {
  status: string;
  message: string;
  data: T;
}

// 简单的HTTP请求工具
class SimpleHttpClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    method: string,
    url: string,
    options: {
      params?: Record<string, any>;
      data?: any;
    } = {}
  ): Promise<T> {
    const { params, data } = options;

    // 构建完整URL
    let fullUrl = `${this.baseURL}${url}`;

    // 添加查询参数
    if (params && Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, String(value));
        }
      });
      const queryString = searchParams.toString();
      if (queryString) {
        fullUrl += `?${queryString}`;
      }
    }

    // 请求配置
    const config: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    // 添加请求体
    if (data && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {
      config.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(fullUrl, config);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (error: any) {
      console.error('请求失败:', error);
      throw error;
    }
  }

  async get<T>(url: string, options?: { params?: Record<string, any> }): Promise<T> {
    return this.request<T>('GET', url, options);
  }

  async post<T>(url: string, data?: any): Promise<T> {
    return this.request<T>('POST', url, { data });
  }

  async put<T>(url: string, data?: any): Promise<T> {
    return this.request<T>('PUT', url, { data });
  }

  async delete<T>(url: string, options?: { data?: any }): Promise<T> {
    return this.request<T>('DELETE', url, options);
  }
}

// 创建HTTP客户端实例
const httpClient = new SimpleHttpClient(API_BASE_PATH);

// 统一的API调用处理
async function apiCall<T>(request: () => Promise<ApiResponse<T>>): Promise<T> {
  try {
    const result = await request();
    console.log('API响应8888:', result);

    if (result.status === 'success') {
      return result.data;
    }
    throw new Error(result.message || '请求失败');
  } catch (error: any) {
    console.error('API请求失败:', error);
    throw error;
  }
}

// 导出类型和实例
export type { ApiResponse };
export { httpClient, apiCall };
